# Branch Form 400 Error Fix

## Problem
When trying to create a branch, the application was getting a 400 Bad Request error:
```
POST http://localhost:3000/api/v1/branches 400 (Bad Request)
Error submitting form: AxiosError {message: 'Request failed with status code 400', ...}
```

## Root Cause
The issue was a **field name mismatch** between the frontend and backend:

- **Frontend** was sending: `{ name: "...", regionId: "..." }`
- **Backend** was expecting: `{ name: "...", region_id: "..." }`

The backend API expects the region field to be named `region_id` (snake_case), but the frontend form was sending `regionId` (camelCase).

## Solution
Updated the `branchApi` methods in `src/contexts/ApiContext.jsx` to transform the data format before sending to the backend.

### Changes Made

#### 1. Create Method (`branchApi.create`)
**Before:**
```javascript
const response = await api.post("/branches", branchData);
```

**After:**
```javascript
// Validate required fields
if (!branchData.name || !branchData.name.trim()) {
  throw new Error("Branch name is required");
}
if (!branchData.regionId) {
  throw new Error("Region is required");
}

// Transform frontend data format to backend expected format
const payload = {
  name: branchData.name.trim(),
  region_id: branchData.regionId, // Backend expects region_id, frontend sends regionId
};

console.log("Creating branch with payload:", payload);
const response = await api.post("/branches", payload);
```

#### 2. Update Method (`branchApi.update`)
Applied the same transformation logic to the update method for consistency.

#### 3. Response Mapping
Updated response mapping to handle the backend's `region_id` field:

**Before:**
```javascript
regionId: createdBranch.regionId || createdBranch.region?.id,
```

**After:**
```javascript
regionId: createdBranch.region_id || createdBranch.regionId || createdBranch.region?.id,
```

#### 4. GetAll Method
Updated the `getAll` method to also handle the `region_id` field in the response mapping.

## Technical Details

### Data Transformation
The fix implements a data transformation layer that:

1. **Outgoing Data (Frontend → Backend):**
   - `regionId` → `region_id`
   - Trims whitespace from string fields
   - Validates required fields

2. **Incoming Data (Backend → Frontend):**
   - `region_id` → `regionId`
   - Handles multiple possible field names for backward compatibility

### Validation Added
- Validates that branch name is provided and not empty
- Validates that region is selected
- Provides clear error messages for validation failures

### Logging
Added console logging to help debug API calls:
```javascript
console.log("Creating branch with payload:", payload);
```

## Expected Backend Format
The backend now receives the correct format:
```json
{
  "name": "Main Street Branch",
  "region_id": "550e8400-e29b-41d4-a716-446655440000"
}
```

## Frontend Form Data
The frontend form continues to use camelCase internally:
```javascript
{
  name: "Main Street Branch",
  regionId: "550e8400-e29b-41d4-a716-446655440000"
}
```

## Benefits of This Approach

1. **No Frontend Changes Required**: The BranchForm component doesn't need to be modified
2. **Backward Compatibility**: Handles multiple possible field names from the backend
3. **Validation**: Adds proper validation before API calls
4. **Consistency**: Same pattern applied to both create and update operations
5. **Debugging**: Added logging for easier troubleshooting

## Testing
After applying this fix:

1. **Create Branch**: Should now work without 400 errors
2. **Update Branch**: Should continue to work with the same data transformation
3. **List Branches**: Should properly map region data from backend responses

## Error Handling
The fix maintains existing error handling while adding:
- Field validation with descriptive error messages
- Proper data transformation logging
- Graceful handling of missing or invalid data

## Future Considerations

### API Standardization
Consider standardizing field naming conventions between frontend and backend:
- Either use camelCase everywhere (frontend preference)
- Or use snake_case everywhere (backend preference)
- Document the chosen convention for consistency

### Type Safety
Consider adding TypeScript interfaces to prevent similar field name mismatches:
```typescript
interface BranchCreateRequest {
  name: string;
  region_id: string;
}

interface BranchFormData {
  name: string;
  regionId: string;
}
```

The branch creation functionality should now work correctly with the backend API!
