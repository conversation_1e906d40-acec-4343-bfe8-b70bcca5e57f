/**
 * Formats today's date in the format "17 Jul 2025"
 * @returns {string} Formatted date string
 */
export const getTodaysDate = () => {
  const today = new Date();

  const day = today.getDate();
  const month = today.toLocaleDateString("en-US", { month: "short" });
  const year = today.getFullYear();

  return `${day} ${month} ${year}`;
};

/**
 * Formats any date string in the format "25 Jul 2025"
 * @param {string} dateString - ISO date string or any valid date string
 * @returns {string} Formatted date string
 */
export const formatDateDisplay = (dateString) => {
  if (!dateString) return "N/A";

  const date = new Date(dateString);
  const options = {
    day: 'numeric',
    month: 'short',
    year: 'numeric'
  };

  return date.toLocaleDateString('en-GB', options);
};

/**
 * Formats a date range in the format "Jul 29 – Aug 4" or "Jul 29 – 4" if same month
 * @param {string} startDate - ISO date string or any valid date string
 * @param {string} endDate - ISO date string or any valid date string
 * @returns {string} Formatted date range string
 */
export const formatDateRange = (startDate, endDate) => {
  if (!startDate || !endDate) return '';

  const start = new Date(startDate);
  const end = new Date(endDate);

  const startMonth = start.toLocaleDateString('en-US', { month: 'short' });
  const startDay = start.getDate();
  const endMonth = end.toLocaleDateString('en-US', { month: 'short' });
  const endDay = end.getDate();

  // If same month, show "Jul 29 – 4"
  if (startMonth === endMonth) {
    return `${startMonth} ${startDay} – ${endDay}`;
  }

  // If different months, show "Jul 29 – Aug 4"
  return `${startMonth} ${startDay} – ${endMonth} ${endDay}`;
};
