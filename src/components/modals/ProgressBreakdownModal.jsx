import { useState, useEffect } from "react";
import { X, Search, Check, X as XIcon } from "lucide-react";
import { formatDateRange } from "../../utils/dateUtils";
import instance from "../../axios/instance";

const ProgressBreakdownModal = ({
  isOpen,
  onClose,
  target,
  onTargetUpdate,
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [editingUserId, setEditingUserId] = useState(null);
  const [editedTargetValues, setEditedTargetValues] = useState({});
  const [showWarningModal, setShowWarningModal] = useState(false);
  const [pendingChange, setPendingChange] = useState(null);
  const [userProgress, setUserProgress] = useState([]);
  const [loading, setLoading] = useState(false);
  const [creatingTarget, setCreatingTarget] = useState(false);

  // Fetch breakdown data when target changes
  useEffect(() => {
    if (target && isOpen) {
      fetchBreakdownData();
    }
  }, [target, isOpen]);

  // Initialize edited target values when user progress data changes
  useEffect(() => {
    if (userProgress.length > 0) {
      const initialValues = {};
      userProgress.forEach((user) => {
        initialValues[user.user_id] = user.target.toString();
      });
      setEditedTargetValues(initialValues);
    }
  }, [userProgress]);

  const fetchBreakdownData = async () => {
    try {
      setLoading(true);
      const response = await instance.get(`/targets/${target.id}/breakdown`);
      console.log("Breakdown data:", response.data);
      setUserProgress(response.data || []);
    } catch (error) {
      console.error("Error fetching breakdown data:", error);
      setUserProgress([]);
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen || !target) return null;

  const handleTargetEdit = (userId) => {
    setEditingUserId(userId);
  };

  const handleTargetSave = (userId) => {
    const newTargetValue = editedTargetValues[userId];
    // Don't allow saving empty values
    if (!newTargetValue || newTargetValue.trim() === "") {
      return;
    }

    setPendingChange({
      userId,
      targetId: target.id,
      newTarget: newTargetValue,
    });
    setShowWarningModal(true);
  };

  const handleTargetCancel = (userId) => {
    // Reset to original value
    const originalUser = userProgress.find((user) => user.user_id === userId);
    setEditedTargetValues((prev) => ({
      ...prev,
      [userId]: originalUser?.target.toString() || "",
    }));
    setEditingUserId(null);
  };

  const handleTargetValueChange = (userId, value) => {
    // Allow empty string or positive numbers only
    if (value === "" || (/^\d+$/.test(value) && parseInt(value) > 0)) {
      setEditedTargetValues((prev) => ({
        ...prev,
        [userId]: value,
      }));
    }
  };

  const createIndividualTarget = async (userId, newTarget) => {
    try {
      setCreatingTarget(true);
      const response = await instance.post(
        `/targets/${target.id}/create-individual-target`,
        {
          userId,
          newTarget,
        }
      );
      console.log("Individual target created:", response.data);

      // Find the user being removed to get their current values
      const removedUser = userProgress.find((user) => user.user_id === userId);

      // Remove user from current list
      setUserProgress((prev) => prev.filter((user) => user.user_id !== userId));

      // Update the role target state and add new individual target via callback
      if (onTargetUpdate) {
        onTargetUpdate(response.data, removedUser, target);
      }

      return response.data;
    } catch (error) {
      console.error("Error creating individual target:", error);
      throw error;
    } finally {
      setCreatingTarget(false);
    }
  };

  // Filter users based on search term
  const filteredUsers = userProgress.filter((user) =>
    user.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const userCount = filteredUsers.length;

  return (
    <div
      className="fixed inset-0 flex items-center justify-center z-50 p-4"
      style={{ backgroundColor: "rgba(0, 0, 0, 0.7)" }}
    >
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl h-[85vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between px-4 py-3 border-b border-gray-200">
          <div>
            <h2 className="text-lg font-semibold text-gray-900">
              Progress Breakdown – {target.assigned_to}
            </h2>
            <div className="text-base text-gray-700 mt-2">
              <div className="mb-1">
                Target Scope: {target.scope === "role" ? "Role" : "Individual"}{" "}
                – {target.assigned_to}
              </div>
              <div className="mb-1">
                Period: {formatDateRange(target.start_date, target.end_date)}
              </div>
              <div className="text-sm text-gray-600">
                {userCount} user{userCount !== 1 ? "s" : ""} using this target
              </div>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X size={20} className="text-gray-500" />
          </button>
        </div>

        {/* Search */}
        <div className="px-4 py-3 border-b border-gray-200">
          <div className="relative">
            <Search
              className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
              size={16}
            />
            <input
              type="text"
              placeholder="Search by name..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* Table */}
        <div className="flex-1 overflow-y-auto">
          <table className="w-full">
            <thead className="bg-gray-50 sticky top-0">
              <tr>
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  User
                </th>
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Progress
                </th>
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {target.metric === "Call" ? "Calls Made" : "Visits Made"}
                </th>
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Target
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {loading ? (
                <tr>
                  <td
                    colSpan={4}
                    className="px-4 py-8 text-center text-gray-500"
                  >
                    <div className="flex items-center justify-center space-x-2">
                      <div className="w-4 h-4 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin"></div>
                      <span>Loading...</span>
                    </div>
                  </td>
                </tr>
              ) : filteredUsers.length === 0 ? (
                <tr>
                  <td
                    colSpan={4}
                    className="px-4 py-8 text-center text-gray-500"
                  >
                    {searchTerm ? "No users found" : "No data available"}
                  </td>
                </tr>
              ) : (
                filteredUsers.map((user) => {
                  const currentValue =
                    target.metric === "Call"
                      ? user.calls_made
                      : user.visits_made;
                  const progress =
                    user.target > 0
                      ? Math.round((currentValue / user.target) * 100)
                      : 0;

                  return (
                    <tr key={user.user_id} className="hover:bg-gray-50">
                      <td className="px-4 py-2">
                        <span className="font-medium text-gray-900">
                          {user.name}
                        </span>
                      </td>
                      <td className="px-4 py-2">
                        <div className="flex items-center space-x-2">
                          <div className="flex-1 bg-gray-200 rounded-full h-2 max-w-[120px]">
                            <div
                              className="bg-green-600 h-2 rounded-full transition-all duration-300"
                              style={{ width: `${progress}%` }}
                            ></div>
                          </div>
                          <span className="text-sm font-medium text-gray-700 min-w-[40px]">
                            {progress}%
                          </span>
                        </div>
                      </td>
                      <td className="px-4 py-2">
                        <span className="text-sm font-semibold text-gray-900">
                          {currentValue}
                        </span>
                      </td>
                      <td className="px-4 py-2">
                        <div className="flex items-center space-x-2">
                          <input
                            type="text"
                            value={
                              editedTargetValues[user.user_id] || user.target
                            }
                            onChange={(e) =>
                              handleTargetValueChange(
                                user.user_id,
                                e.target.value
                              )
                            }
                            className="w-16 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:border-gray-400"
                            placeholder="0"
                          />
                          {editedTargetValues[user.user_id] !== undefined &&
                            editedTargetValues[user.user_id] !==
                              user.target.toString() && (
                              <>
                                <button
                                  onClick={() =>
                                    handleTargetCancel(user.user_id)
                                  }
                                  className="p-1 hover:bg-gray-100 rounded transition-colors"
                                  title="Cancel"
                                >
                                  <XIcon
                                    size={14}
                                    className="text-gray-500 hover:text-red-500"
                                  />
                                </button>
                                <button
                                  onClick={() => handleTargetSave(user.user_id)}
                                  className="p-1 hover:bg-gray-100 rounded transition-colors"
                                  title="Save"
                                >
                                  <Check
                                    size={14}
                                    className="text-gray-500 hover:text-green-500"
                                  />
                                </button>
                              </>
                            )}
                        </div>
                      </td>
                    </tr>
                  );
                })
              )}
            </tbody>
          </table>
        </div>

        {/* Footer */}
        <div className="px-4 py-3 border-t border-gray-200 bg-gray-50 rounded-b-lg">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Close
          </button>
        </div>
      </div>

      {/* Warning Modal */}
      {showWarningModal && (
        <div
          className="fixed inset-0 flex items-center justify-center z-[60] p-4"
          style={{ backgroundColor: "rgba(0, 0, 0, 0.7)" }}
        >
          <div className="bg-white rounded-lg shadow-xl w-full max-w-md">
            <div className="p-6">
              <div className="text-center">
                <div className="mx-auto flex items-center justify-center w-12 h-12 rounded-full bg-orange-100 mb-4">
                  <span className="text-orange-500 text-xl font-bold">!</span>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  Change Target Warning
                </h3>
                <p className="text-sm text-gray-600 mb-6">
                  The user will stop using this target and start using an
                  individual target.
                </p>
                <div className="flex gap-3">
                  <button
                    onClick={() => {
                      setShowWarningModal(false);
                      setPendingChange(null);
                    }}
                    className="flex-1 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={async () => {
                      // Handle the individual target change here
                      if (pendingChange) {
                        try {
                          console.log("Individual target change:", {
                            userId: pendingChange.userId,
                            newTarget: pendingChange.newTarget,
                          });

                          await createIndividualTarget(
                            pendingChange.userId,
                            pendingChange.newTarget
                          );
                        } catch (error) {
                          console.error(
                            "Failed to create individual target:",
                            error
                          );
                        }
                      }
                      setShowWarningModal(false);
                      setEditingUserId(null);
                      setPendingChange(null);
                    }}
                    disabled={creatingTarget}
                    className="flex-1 px-4 py-2 text-sm font-medium text-white bg-orange-600 hover:bg-orange-700 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {creatingTarget ? (
                      <div className="flex items-center justify-center space-x-2">
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        <span>Creating...</span>
                      </div>
                    ) : (
                      "Confirm"
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProgressBreakdownModal;
