// Helper function to calculate time difference and get color coding
const getTimeStatus = (dueDate, currentTime) => {
  const due = new Date(dueDate);
  const diffMs = due.getTime() - currentTime.getTime();
  const diffHours = diffMs / (1000 * 60 * 60);
  const diffMinutes = Math.abs(diffMs) / (1000 * 60);

  if (diffMs < 0) {
    // Overdue
    const hours = Math.floor(diffMinutes / 60);
    const minutes = Math.floor(diffMinutes % 60);
    return {
      text: `Overdue by ${hours}h ${minutes}m`,
      color: "text-red-600 dark:text-red-400",
      bgColor: "bg-red-50 dark:bg-red-900/20",
      emoji: "🔴",
    };
  } else if (diffHours < 2) {
    // Less than 2 hours
    const hours = Math.floor(diffMinutes / 60);
    const minutes = Math.floor(diffMinutes % 60);
    return {
      text: `${hours}h ${minutes}m`,
      color: "text-red-600 dark:text-red-400",
      bgColor: "bg-red-50 dark:bg-red-900/20",
      emoji: "🔴",
    };
  } else if (diffHours <= 12) {
    // Between 2-12 hours
    const hours = Math.floor(diffMinutes / 60);
    const minutes = Math.floor(diffMinutes % 60);
    return {
      text: `${hours}h ${minutes}m`,
      color: "text-yellow-600 dark:text-yellow-400",
      bgColor: "bg-yellow-50 dark:bg-yellow-900/20",
      emoji: "🟡",
    };
  } else {
    // More than 12 hours
    const hours = Math.floor(diffMinutes / 60);
    const minutes = Math.floor(diffMinutes % 60);
    return {
      text: `${hours}h ${minutes}m`,
      color: "text-green-600 dark:text-green-400",
      bgColor: "bg-green-50 dark:bg-green-900/20",
      emoji: "🟢",
    };
  }
};

// Hitlist-specific columns (without Visit Stage and Due In)
export const hitlistColumns = [
  {
    key: "clientId",
    title: "CLIENT ID",
    render: (value) => (
      <span className="font-medium text-gray-900 dark:text-white">{value}</span>
    ),
  },
  {
    key: "clientName",
    title: "CLIENT NAME",
    render: (value) => (
      <span className="text-sm text-gray-600 dark:text-gray-400">{value}</span>
    ),
  },
  {
    key: "phoneNumber",
    title: "PHONE NUMBER",
    render: (value) => (
      <span className="text-sm text-gray-600 dark:text-gray-400 font-mono">
        {value}
      </span>
    ),
  },
  {
    key: "accountNumber",
    title: "ACCOUNT NUMBER",
    render: (value) => (
      <span className="text-sm text-gray-600 dark:text-gray-400 font-mono">
        {value || "N/A"}
      </span>
    ),
  },
  {
    key: "productName",
    title: "PRODUCT NAME",
    render: (value) => (
      <span className="text-sm text-gray-600 dark:text-gray-400">{value}</span>
    ),
  },
  {
    key: "dateOpened",
    title: "DATE OPENED",
    render: (value) => (
      <span className="text-sm text-gray-600 dark:text-gray-400">
        {new Date(value).toLocaleDateString()}
      </span>
    ),
  },
  {
    key: "assignedOfficer",
    title: "ASSIGNED OFFICER",
    render: (value) => (
      <span className="text-sm text-gray-600 dark:text-gray-400">
        {value || "N/A"}
      </span>
    ),
  },
  {
    key: "branchName",
    title: "BRANCH NAME",
    render: (value) => (
      <span className="text-sm text-gray-600 dark:text-gray-400">{value}</span>
    ),
  },
  {
    key: "dueDate",
    title: "DUE DATE",
    render: (value) => (
      <div className="text-sm">
        <div className="text-gray-900 dark:text-white font-medium">
          {new Date(value).toLocaleDateString()}
        </div>
        <div className="text-gray-500 dark:text-gray-400 text-xs">
          {new Date(value).toLocaleTimeString([], {
            hour: "2-digit",
            minute: "2-digit",
          })}
        </div>
      </div>
    ),
  },
  {
    key: "segment",
    title: "SEGMENT",
    render: (value) => (
      <span className="text-sm text-gray-600 dark:text-gray-400">
        {value || "N/A"}
      </span>
    ),
  },
];

// Other tabs columns (simplified structure)
export const otherTabsColumns = [
  {
    key: "clientId",
    title: "CLIENT ID",
    render: (value) => (
      <span className="font-medium text-gray-900 dark:text-white">{value}</span>
    ),
  },
  {
    key: "clientName",
    title: "CLIENT NAME",
    render: (value) => (
      <span className="text-sm text-gray-600 dark:text-gray-400">{value}</span>
    ),
  },
  {
    key: "assignedTo",
    title: "ASSIGNED TO",
    render: (value) => (
      <span className="text-sm text-gray-600 dark:text-gray-400">
        {value || "N/A"}
      </span>
    ),
  },
  {
    key: "dueDate",
    title: "DUE DATE",
    render: (value) => (
      <div className="text-sm">
        <div className="text-gray-900 dark:text-white font-medium">
          {new Date(value).toLocaleDateString()}
        </div>
        <div className="text-gray-500 dark:text-gray-400 text-xs">
          {new Date(value).toLocaleTimeString([], {
            hour: "2-digit",
            minute: "2-digit",
          })}
        </div>
      </div>
    ),
  },
  {
    key: "dueDate",
    title: "DUE IN",
    render: (value, _, currentTime) => {
      const timeStatus = getTimeStatus(value, currentTime);
      return (
        <div
          className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${timeStatus.bgColor}`}
        >
          <span className="mr-1">{timeStatus.emoji}</span>
          <span className={timeStatus.color}>{timeStatus.text}</span>
        </div>
      );
    },
  },
];
