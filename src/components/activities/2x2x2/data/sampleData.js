// Helper to create dates for testing different time statuses
const now = new Date();
const getTestDate = (hoursFromNow) => {
  const date = new Date(now);
  date.setHours(date.getHours() + hoursFromNow);
  return date.toISOString();
};

// All activities data for all tabs
export const allActivities = {
  hitlist: [
    {
      id: "hitlist_001",
      clientId: "CL001",
      clientName: "<PERSON>",
      phoneNumber: "+254 712 345 678",
      accountNumber: "ACC001234567",
      productName: "Savings Account",
      dateOpened: "2024-01-15T09:00:00Z",
      assignedOfficer: "<PERSON>",
      branchName: "Nairobi Main",
      dueDate: getTestDate(1), // 1 hour from now (red)
      segment: "Retail",
    },
    {
      id: "hitlist_002",
      clientId: "CL002",
      clientName: "Mary Wanjiku",
      phoneNumber: "+254 723 456 789",
      accountNumber: null, // Will show N/A
      productName: "Current Account",
      dateOpened: "2024-01-20T14:30:00Z",
      assignedOfficer: null, // Will show N/A
      branchName: "Westlands",
      dueDate: getTestDate(6), // 6 hours from now (yellow)
      segment: "Micro",
    },
    {
      id: "hitlist_003",
      clientId: "CL003",
      clientName: "Peter Mwangi",
      phoneNumber: "+254 734 567 890",
      accountNumber: "ACC003456789",
      productName: "Personal Loan",
      dateOpened: "2024-01-25T11:15:00Z",
      assignedOfficer: "David Brown",
      branchName: "Karen",
      dueDate: getTestDate(24), // 24 hours from now (green)
      segment: "Diaspora",
    },
    {
      id: "hitlist_004",
      clientId: "CL004",
      clientName: "Grace Akinyi",
      phoneNumber: "+254 745 678 901",
      accountNumber: "ACC004567890",
      productName: "Fixed Deposit",
      dateOpened: "2024-02-01T16:45:00Z",
      assignedOfficer: "Emily Davis",
      branchName: "Mombasa Road",
      dueDate: getTestDate(-2), // 2 hours overdue (red overdue)
      segment: null, // Will show N/A
    },
    {
      id: "hitlist_005",
      clientId: "CL005",
      clientName: "James Ochieng",
      phoneNumber: "+254 756 789 012",
      accountNumber: null,
      productName: "Business Loan",
      dateOpened: "2024-02-05T08:30:00Z",
      assignedOfficer: null,
      branchName: "Industrial Area",
      dueDate: getTestDate(3), // 3 hours from now (yellow)
      segment: "Corporate",
    },
  ],
  first2: [
    {
      id: "first2_001",
      clientId: "CL005",
      clientName: "James Ochieng",
      assignedTo: "Robert Miller",
      dueDate: getTestDate(3), // 3 hours from now (yellow)
    },
    {
      id: "first2_002",
      clientId: "CL006",
      clientName: "Alice Wanjiru",
      assignedTo: "Tom Anderson",
      dueDate: getTestDate(15), // 15 hours from now (green)
    },
    {
      id: "first2_003",
      clientId: "CL007",
      clientName: "Samuel Kiprotich",
      assignedTo: "Lisa Johnson",
      dueDate: getTestDate(0.5), // 30 minutes from now (red)
    },
  ],
  second2: [
    {
      id: "second2_001",
      clientId: "CL008",
      clientName: "Ruth Muthoni",
      assignedTo: "Mark Wilson",
      dueDate: getTestDate(8), // 8 hours from now (yellow)
    },
    {
      id: "second2_002",
      clientId: "CL009",
      clientName: "Daniel Kipchoge",
      assignedTo: "Sarah Brown",
      dueDate: getTestDate(-5), // 5 hours overdue (red overdue)
    },
    {
      id: "second2_003",
      clientId: "CL010",
      clientName: "Catherine Njeri",
      assignedTo: "Paul Anderson",
      dueDate: getTestDate(48), // 48 hours from now (green)
    },
  ],
  third2: [
    {
      id: "third2_001",
      clientId: "CL011",
      clientName: "Moses Kiprotich",
      assignedTo: "Jane Smith",
      dueDate: getTestDate(1.5), // 1.5 hours from now (red)
    },
    {
      id: "third2_002",
      clientId: "CL012",
      clientName: "Rebecca Wanjiku",
      assignedTo: "David Johnson",
      dueDate: getTestDate(10), // 10 hours from now (yellow)
    },
    {
      id: "third2_003",
      clientId: "CL013",
      clientName: "Francis Mwangi",
      assignedTo: "Mary Wilson",
      dueDate: getTestDate(72), // 72 hours from now (green)
    },
  ],
};
