import DataTable from "../../../common/DataTable";
import { hitlistColumns } from "../config/columns.jsx";
import { allActivities } from "../data/sampleData";

const HitlistTab = ({
  loading,
  loadingMore,
  onView,
  onLoadMore,
  customHeaderContent,
}) => {
  // Handle import, export, and print actions
  const handleImport = () => {
    console.log("Import hitlist data");
    // TODO: Implement import functionality
  };

  const handleExport = () => {
    console.log("Export hitlist data");
    // TODO: Implement export functionality
  };

  const handlePrint = () => {
    console.log("Print hitlist data");
    // TODO: Implement print functionality
  };

  const handleCreate = () => {
    console.log("Create new hitlist entry");
    // TODO: Implement create functionality
  };

  return (
    <DataTable
      key="hitlist-table"
      columns={hitlistColumns}
      data={allActivities.hitlist}
      searchPlaceholder="Search hitlist..."
      addButtonText="New Hitlist Entry"
      onView={onView}
      onAdd={handleCreate}
      actions={["edit", "delete"]}
      loading={loading}
      loadingMore={loadingMore}
      loadMoreText="Load More Hitlist Entries"
      onLoadMore={onLoadMore}
      showLoadMore={true}
      dataCountLabel="hitlist"
      customHeaderContent={customHeaderContent}
      createModalTitle="Create New Hitlist Entry"
      editModalTitle="Edit Hitlist Entry"
      deleteModalTitle="Delete Hitlist Entry"
      modalSize="lg"
      // Enable import, export, and print
      showImportExport={true}
      onImport={handleImport}
      onExport={handleExport}
      onPrint={handlePrint}
    />
  );
};

export default HitlistTab;
