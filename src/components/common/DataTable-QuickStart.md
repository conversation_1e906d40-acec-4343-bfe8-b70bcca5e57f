# DataTable Quick Start Guide

## Adding Data Count and Filters to Any Page

### Step 1: Add Filter State

```jsx
// Add this to your page component
const [filters, setFilters] = useState({
  // Add your filter keys here
  category: "",
  status: "",
  // ... more filters as needed
});
```

### Step 2: Create Filter Configuration

```jsx
const filterConfig = [
  {
    key: "category", // Must match key in filters state
    label: "Categories", // Used in "All Categories" placeholder
    field: "categoryField", // Field name in your data objects
    placeholder: "All Categories", // Optional custom placeholder
    selectedValue: filters.category,
    options: [
      { value: "option1", label: "Option 1" },
      { value: "option2", label: "Option 2" },
      // ... your options
    ],
  },
  // Add more filters as needed
];
```

### Step 3: Add Handlers

```jsx
const handleFilterChange = (filterKey, value) => {
  setFilters((prev) => ({
    ...prev,
    [filterKey]: value,
  }));
};

const handleClearFilters = () => {
  setFilters({
    category: "",
    status: "",
    // Reset all your filter keys
  });
};
```

### Step 4: Update DataTable Props

```jsx
<DataTable
  // ... your existing props

  // Data count (automatic for all pages)
  dataCountLabel="your-items" // e.g., "products", "users", "leads"
  // Filters (optional)
  showFilters={true}
  filterConfig={filterConfig}
  onFilterChange={handleFilterChange}
  onClearFilters={handleClearFilters}
/>
```

## Complete Example

```jsx
import { useState } from "react";
import DataTable from "../components/common/DataTable";

const MyPage = () => {
  // 1. Filter state
  const [filters, setFilters] = useState({
    branch: "",
    status: "",
  });

  // 2. Filter configuration
  const filterConfig = [
    {
      key: "branch",
      label: "Branches",
      field: "branchName",
      placeholder: "All Branches",
      selectedValue: filters.branch,
      options: [
        { value: "Branch A", label: "Branch A" },
        { value: "Branch B", label: "Branch B" },
      ],
    },
    {
      key: "status",
      label: "Status",
      field: "status",
      placeholder: "All Status",
      selectedValue: filters.status,
      options: [
        { value: "Active", label: "Active" },
        { value: "Inactive", label: "Inactive" },
      ],
    },
  ];

  // 3. Handlers
  const handleFilterChange = (filterKey, value) => {
    setFilters((prev) => ({
      ...prev,
      [filterKey]: value,
    }));
  };

  const handleClearFilters = () => {
    setFilters({
      branch: "",
      status: "",
    });
  };

  return (
    <DataTable
      columns={columns}
      data={data}
      dataCountLabel="items"
      showFilters={true}
      filterConfig={filterConfig}
      onFilterChange={handleFilterChange}
      onClearFilters={handleClearFilters}
      // ... other props
    />
  );
};
```

## Common Filter Patterns

### Branch Filter

```jsx
{
  key: "branch",
  label: "Branches",
  field: "branchName",
  placeholder: "All Branches",
  selectedValue: filters.branch,
  options: branchOptions, // Your branch options array
}
```

### Status Filter

```jsx
{
  key: "status",
  label: "Status",
  field: "status",
  placeholder: "All Status",
  selectedValue: filters.status,
  options: [
    { value: "Active", label: "Active" },
    { value: "Inactive", label: "Inactive" },
  ],
}
```

### Role Filter

```jsx
{
  key: "role",
  label: "Roles",
  field: "role",
  placeholder: "All Roles",
  selectedValue: filters.role,
  options: [
    { value: "Admin", label: "Administrator" },
    { value: "User", label: "User" },
    { value: "Manager", label: "Manager" },
  ],
}
```

### Type Filter

```jsx
{
  key: "type",
  label: "Types",
  field: "type",
  placeholder: "All Types",
  selectedValue: filters.type,
  options: [
    { value: "New", label: "New" },
    { value: "Existing", label: "Existing" },
  ],
}
```

## Data Count Labels by Page

- **Leads**: `dataCountLabel="leads"`
- **Users**: `dataCountLabel="users"`
- **Products**: `dataCountLabel="products"`
- **Orders**: `dataCountLabel="orders"`
- **Tickets**: `dataCountLabel="tickets"`
- **Activities**: `dataCountLabel="activities"`
- **Calls**: `dataCountLabel="calls"`
- **Visits**: `dataCountLabel="visits"`

## Tips

1. **Keep filter keys simple**: Use lowercase, no spaces (e.g., "branch", "status")
2. **Match field names**: Ensure `field` matches your data object properties
3. **Consistent options**: Use the same format for all option arrays
4. **Clear state**: Always reset all filter keys in `handleClearFilters`
5. **Test filtering**: Verify that your data has the fields you're filtering by

## Date Range Filter

### Adding Date Range Filter

```jsx
// 1. Add date range state
const [fromDate, setFromDate] = useState("");
const [toDate, setToDate] = useState("");

// 2. Add handlers
const handleFromDateChange = (date) => {
  setFromDate(date);
};

const handleToDateChange = (date) => {
  setToDate(date);
};

// 3. Add to DataTable
<DataTable
  // ... other props
  showDateRangeFilter={true}
  dateRangeField="date" // Field in your data to filter by
  fromDate={fromDate}
  toDate={toDate}
  onFromDateChange={handleFromDateChange}
  onToDateChange={handleToDateChange}
/>;
```

### Date Range Configuration

- **Field Mapping**: Use `dateRangeField` to specify which field in your data contains the date
- **Inclusive Filtering**: From date is inclusive from start of day, To date is inclusive to end of day
- **Date Format**: Accepts any valid date string that can be parsed by `new Date()`
- **Label Position**: Labels are positioned to the left of inputs to match current design

## Result

After implementing these steps, your page will have:

- ✅ "Showing X of Y [items]" text at the top
- ✅ Filter dropdowns next to Import/Export buttons
- ✅ Date range filter above search bar (optional)
- ✅ Real-time filtering that works with search
- ✅ Clear button to reset all filters
- ✅ Consistent UI across all pages
