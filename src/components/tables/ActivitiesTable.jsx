import React, { useState, useMemo } from "react";
import { DataGrid } from "@mui/x-data-grid";
import {
  Box,
  FormControl,
  Select,
  MenuItem,
  Typography,
  Paper,
} from "@mui/material";
import { createTheme, ThemeProvider } from "@mui/material/styles";

function ActivitiesTable() {
  const [statusFilter, setStatusFilter] = useState("all");
  const [activityTypeFilter, setActivityTypeFilter] = useState("all");

  // Create custom theme for professional styling
  const theme = createTheme({
    components: {
      MuiDataGrid: {
        styleOverrides: {
          root: {
            border: "none",
            "& .MuiDataGrid-columnHeaders": {
              backgroundColor: "#1c5b41 !important",
              borderBottom: "1px solid #e2e8f0",
              "& .MuiDataGrid-columnHeader": {
                backgroundColor: "#1c5b41 !important",
                borderRight: "1px solid #ffffff",
                "&:focus": {
                  outline: "none",
                },
                "&:last-child": {
                  borderRight: "none",
                },
              },
            },
            "& .MuiDataGrid-columnHeaderTitle": {
              fontWeight: 600,
              fontSize: "0.75rem",
              color: "#ffffff !important",
              textTransform: "uppercase",
              letterSpacing: "0.05em",
            },
            "& .MuiDataGrid-cell": {
              borderBottom: "1px solid #f1f5f9",
              borderRight: "1px solid #e2e8f0",
              fontSize: "15px",
              "&:focus": {
                outline: "none",
              },
              "&:last-child": {
                borderRight: "none",
              },
            },
            "& .MuiDataGrid-row": {
              "&:nth-of-type(odd)": {
                backgroundColor: "#f8fcf6",
                "&:hover": {
                  backgroundColor: "#f8fcf6",
                },
              },
              "&:nth-of-type(even)": {
                backgroundColor: "#ffffff",
                "&:hover": {
                  backgroundColor: "#ffffff",
                },
              },
            },
          },
        },
      },
    },
  });

  // Test data for activities
  const activitiesData = [
    {
      id: 1,
      customerName: "ABC Manufacturing Ltd",
      staffName: "John Smith",
      activityType: "Call",
      scheduledDate: "2025-01-23",
      scheduledTime: "09:30",
      status: "Upcoming",
      contactInfo: "+1 234-567-8901",
      notes: "Follow up on quotation request",
    },
    {
      id: 2,
      customerName: "Tech Solutions Inc",
      staffName: "Sarah Johnson",
      activityType: "Visit",
      scheduledDate: "2025-01-23",
      scheduledTime: "14:00",
      status: "Scheduled",
      contactInfo: "<EMAIL>",
      notes: "Product demonstration and contract signing",
    },
    {
      id: 3,
      customerName: "Global Enterprises",
      staffName: "Mike Davis",
      activityType: "Call",
      scheduledDate: "2025-01-22",
      scheduledTime: "11:15",
      status: "Overdue",
      contactInfo: "+1 234-567-8902",
      notes: "Discuss payment terms",
    },
    {
      id: 4,
      customerName: "Innovation Corp",
      staffName: "Emily Brown",
      activityType: "Visit",
      scheduledDate: "2025-01-24",
      scheduledTime: "10:00",
      status: "Scheduled",
      contactInfo: "+1 234-567-8903",
      notes: "Site inspection and requirements gathering",
    },
    {
      id: 5,
      customerName: "Metro Services",
      staffName: "David Wilson",
      activityType: "Call",
      scheduledDate: "2025-01-21",
      scheduledTime: "16:30",
      status: "Overdue",
      contactInfo: "<EMAIL>",
      notes: "Project status update",
    },
    {
      id: 6,
      customerName: "Prime Industries",
      staffName: "Lisa Anderson",
      activityType: "Visit",
      scheduledDate: "2025-01-23",
      scheduledTime: "13:30",
      status: "Upcoming",
      contactInfo: "+1 234-567-8904",
      notes: "Equipment maintenance check",
    },
    {
      id: 7,
      customerName: "Future Tech Ltd",
      staffName: "Tom Miller",
      activityType: "Call",
      scheduledDate: "2025-01-24",
      scheduledTime: "15:45",
      status: "Scheduled",
      contactInfo: "+1 234-567-8905",
      notes: "Technical support consultation",
    },
    {
      id: 8,
      customerName: "Dynamic Solutions",
      staffName: "Anna Garcia",
      activityType: "Visit",
      scheduledDate: "2025-01-22",
      scheduledTime: "09:00",
      status: "Overdue",
      contactInfo: "<EMAIL>",
      notes: "Contract renewal discussion",
    },
  ];

  // Define columns for DataGrid
  const columns = [
    {
      field: "customerName",
      headerName: "CUSTOMER NAME",
      flex: 1.5,
      minWidth: 200,
      renderCell: (params) => (
        <Typography
          variant="body2"
          sx={{ fontWeight: 500, color: "#1f2937", fontSize: "15px" }}
        >
          {params.value}
        </Typography>
      ),
    },
    {
      field: "staffName",
      headerName: "STAFF NAME",
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Typography variant="body2" sx={{ color: "#6b7280", fontSize: "15px" }}>
          {params.value}
        </Typography>
      ),
    },
    {
      field: "activityType",
      headerName: "ACTIVITY TYPE",
      flex: 0.8,
      minWidth: 120,
      renderCell: (params) => (
        <Typography variant="body2" sx={{ color: "#6b7280", fontSize: "15px" }}>
          {params.value}
        </Typography>
      ),
    },
    {
      field: "scheduledDate",
      headerName: "SCHEDULED DATE",
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Box sx={{ display: "flex", alignItems: "center", gap: "8px" }}>
          <Typography
            variant="body2"
            sx={{ fontWeight: 500, color: "#1f2937", fontSize: "15px" }}
          >
            {params.row.scheduledDate}
          </Typography>
          <Typography
            variant="body2"
            sx={{ color: "#9ca3af", fontSize: "15px" }}
          >
            {params.row.scheduledTime}
          </Typography>
        </Box>
      ),
    },
    {
      field: "status",
      headerName: "STATUS",
      flex: 0.8,
      minWidth: 100,
      renderCell: (params) => {
        const getStatusColor = (status) => {
          switch (status.toLowerCase()) {
            case "upcoming":
              return "#1E3A8A";
            case "scheduled":
              return "#10b981";
            case "overdue":
              return "#ef4444";
            default:
              return "#6b7280";
          }
        };
        return (
          <Typography
            variant="body2"
            sx={{
              color: getStatusColor(params.value),
              fontWeight: 500,
              fontSize: "15px",
            }}
          >
            {params.value}
          </Typography>
        );
      },
    },
    {
      field: "contactInfo",
      headerName: "CONTACT INFO",
      flex: 1.2,
      minWidth: 180,
      renderCell: (params) => (
        <Typography variant="body2" sx={{ color: "#6b7280", fontSize: "15px" }}>
          {params.value}
        </Typography>
      ),
    },
    {
      field: "notes",
      headerName: "NOTES",
      flex: 1.5,
      minWidth: 200,
      renderCell: (params) => (
        <Typography
          variant="body2"
          sx={{
            color: "#6b7280",
            fontSize: "15px",
            overflow: "hidden",
            textOverflow: "ellipsis",
            whiteSpace: "nowrap",
          }}
        >
          {params.value}
        </Typography>
      ),
    },
  ];

  // Filter data based on selected filters
  const filteredData = useMemo(() => {
    return activitiesData.filter((activity) => {
      const statusMatch =
        statusFilter === "all" ||
        activity.status.toLowerCase() === statusFilter.toLowerCase();
      const typeMatch =
        activityTypeFilter === "all" ||
        activity.activityType.toLowerCase() ===
          activityTypeFilter.toLowerCase();
      return statusMatch && typeMatch;
    });
  }, [statusFilter, activityTypeFilter]);

  return (
    <ThemeProvider theme={theme}>
      <Paper
        elevation={3}
        sx={{
          borderRadius: "12px",
          padding: "24px",
          marginBottom: "32px",
          backgroundColor: "white",
        }}
      >
        {/* Header */}
        <Box sx={{ marginBottom: "24px" }}>
          <Typography
            variant="h5"
            sx={{
              fontWeight: 600,
              color: "#1f2937",
              marginBottom: "8px",
              fontSize: "1.4rem",
            }}
          >
            ACTIVITIES SCHEDULE
          </Typography>
          <Typography
            variant="body2"
            sx={{ color: "#6b7280", fontSize: "15px" }}
          >
            Shows a summary of upcoming, scheduled and overdue calls and visits
          </Typography>
        </Box>

        {/* Filter Dropdowns */}
        <Box
          sx={{
            display: "flex",
            gap: "32px",
            marginBottom: "24px",
            flexWrap: "wrap",
            alignItems: "center",
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", gap: "12px" }}>
            <Typography
              sx={{
                fontSize: "0.75rem",
                fontWeight: 600,
                color: "#6b7280",
                textTransform: "uppercase",
                letterSpacing: "0.05em",
                minWidth: "60px",
              }}
            >
              STATUS:
            </Typography>
            <FormControl size="small" sx={{ minWidth: 120 }}>
              <Select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                displayEmpty
                sx={{
                  fontSize: "0.875rem",
                  "& .MuiOutlinedInput-notchedOutline": {
                    borderColor: "#e5e7eb",
                  },
                  "&:hover .MuiOutlinedInput-notchedOutline": {
                    borderColor: "#d1d5db",
                  },
                  "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                    borderColor: "#3b82f6",
                  },
                }}
              >
                <MenuItem value="all">All</MenuItem>
                <MenuItem value="upcoming">Upcoming</MenuItem>
                <MenuItem value="scheduled">Scheduled</MenuItem>
                <MenuItem value="overdue">Overdue</MenuItem>
              </Select>
            </FormControl>
          </Box>

          <Box sx={{ display: "flex", alignItems: "center", gap: "12px" }}>
            <Typography
              sx={{
                fontSize: "0.75rem",
                fontWeight: 600,
                color: "#6b7280",
                textTransform: "uppercase",
                letterSpacing: "0.05em",
                minWidth: "100px",
              }}
            >
              ACTIVITY TYPE:
            </Typography>
            <FormControl size="small" sx={{ minWidth: 140 }}>
              <Select
                value={activityTypeFilter}
                onChange={(e) => setActivityTypeFilter(e.target.value)}
                displayEmpty
                sx={{
                  fontSize: "0.875rem",
                  "& .MuiOutlinedInput-notchedOutline": {
                    borderColor: "#e5e7eb",
                  },
                  "&:hover .MuiOutlinedInput-notchedOutline": {
                    borderColor: "#d1d5db",
                  },
                  "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                    borderColor: "#3b82f6",
                  },
                }}
              >
                <MenuItem value="all">All</MenuItem>
                <MenuItem value="call">Call</MenuItem>
                <MenuItem value="visit">Visit</MenuItem>
              </Select>
            </FormControl>
          </Box>
        </Box>

        {/* DataGrid */}
        <Box sx={{ height: 400, width: "100%" }}>
          <DataGrid
            rows={filteredData}
            columns={columns}
            initialState={{
              pagination: {
                paginationModel: { page: 0, pageSize: 5 },
              },
            }}
            pageSizeOptions={[5, 10, 25]}
            disableRowSelectionOnClick
            disableColumnMenu
            hideFooterSelectedRowCount
            sx={{
              "& .MuiDataGrid-columnHeaders": {
                backgroundColor: "#1c5b41 !important",
              },
              "& .MuiDataGrid-columnHeader": {
                backgroundColor: "#1c5b41 !important",
              },
              "& .MuiDataGrid-columnHeaderTitle": {
                color: "#ffffff !important",
              },
              "& .MuiDataGrid-footerContainer": {
                borderTop: "1px solid #e5e7eb",
                backgroundColor: "#f8fafc",
              },
              "& .MuiTablePagination-root": {
                fontSize: "0.875rem",
                color: "#6b7280",
              },
            }}
          />
        </Box>

        {/* Summary */}
        <Box
          sx={{
            marginTop: "16px",
            paddingTop: "16px",
            borderTop: "1px solid #e5e7eb",
          }}
        >
          <Typography variant="body2" sx={{ color: "#6b7280" }}>
            Showing {filteredData.length} of {activitiesData.length} activities
          </Typography>
        </Box>
      </Paper>
    </ThemeProvider>
  );
}

export default ActivitiesTable;
