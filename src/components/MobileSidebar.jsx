import { useState } from "react";
import { Link, useLocation } from "react-router-dom";
import { useApp } from "../contexts/AppContext";
import { menuItems, isRouteActive } from "../config/menuItems";
import logoSmall from "../assets/images/logo.png";
import logoText from "../assets/images/logo-text.png";
import { ArrowLeft, CalendarDays, ChevronRight, Heart } from "lucide-react";
import { getTodaysDate } from "../utils/dateUtils";

function MobileSidebar() {
  const { sidebarOpen, closeMobileSidebar } = useApp();
  const location = useLocation();
  const [expandedItems, setExpandedItems] = useState({});

  // Function to check if a menu item is active (supports nested children)
  const isItemActive = (item) => {
    // Use regex-based matching for better route detection
    if (isRouteActive(location.pathname, item)) {
      return true;
    }
    // Check if any child is active (supports nested children)
    if (item.children) {
      return item.children.some((child) => {
        if (isRouteActive(location.pathname, child)) {
          return true;
        }
        // Check nested children
        if (child.children) {
          return child.children.some((grandchild) =>
            isRouteActive(location.pathname, grandchild)
          );
        }
        return false;
      });
    }
    return false;
  };

  const toggleExpanded = (itemId) => {
    setExpandedItems((prev) => ({
      ...prev,
      [itemId]: !prev[itemId],
    }));
  };

  // Recursive function to render menu items with support for multiple levels
  const renderMenuItem = (item, level = 0) => {
    const isExpanded = expandedItems[item.id];
    const hasChildren = item.hasChildren && item.children;
    const isActive = isItemActive(item);

    const linkContent = (
      <>
        <span className="mr-3 flex-shrink-0">
          {typeof item.icon === "string" ? (
            item.icon
          ) : item.icon ? (
            <item.icon size={20} className="text-current" />
          ) : level === 1 ? (
            <span className="text-current opacity-60 font-bold">-</span>
          ) : level >= 2 ? (
            <span className="text-current opacity-60">•</span>
          ) : null}
        </span>
        <span className="transition-opacity duration-300">{item.label}</span>
        {hasChildren && (
          <span
            className={`ml-auto transition-transform ${
              isExpanded ? "rotate-90" : ""
            }`}
          >
            <ChevronRight size={18} />
          </span>
        )}
      </>
    );

    const baseClasses =
      "flex items-center px-3 py-2 rounded-lg transition-colors duration-200";

    const activeClasses =
      level === 0
        ? "text-white bg-[#1c5b41] dark:bg-[#1c5b41]"
        : "text-[#1c5b41] dark:text-[#1c5b41] font-semibold";

    const inactiveClasses =
      level === 0
        ? "text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800"
        : "text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-800";

    return (
      <li key={item.id}>
        {hasChildren ? (
          <button
            onClick={() => toggleExpanded(item.id)}
            className={`w-full ${baseClasses} ${
              isActive ? activeClasses : inactiveClasses
            } ${level > 0 ? "text-sm" : ""}`}
          >
            {linkContent}
          </button>
        ) : item.href !== "#" ? (
          <Link
            to={item.href}
            onClick={closeMobileSidebar}
            className={`${baseClasses} ${
              isActive ? activeClasses : inactiveClasses
            } ${level > 0 ? "text-sm" : ""}`}
          >
            {linkContent}
          </Link>
        ) : (
          <span
            className={`${baseClasses} ${
              isActive ? activeClasses : inactiveClasses
            } ${level > 0 ? "text-sm" : ""}`}
          >
            {linkContent}
          </span>
        )}

        {hasChildren && isExpanded && (
          <ul className={`${level === 0 ? "ml-6" : "ml-4"} mt-2 space-y-1`}>
            {item.children.map((child) => renderMenuItem(child, level + 1))}
          </ul>
        )}
      </li>
    );
  };

  return (
    <>
      {/* Overlay - animated fade in/out */}
      <div
        className={`fixed inset-0 bg-[rgba(0,0,0,0.3)] dark:bg-[rgba(0,0,0,0.4)] z-10 transition-opacity duration-300 ${
          sidebarOpen ? "opacity-100" : "opacity-0 pointer-events-none"
        }`}
        onClick={closeMobileSidebar}
      />

      {/* Mobile Sidebar - animated slide in/out */}
      <aside
        id="mobile-sidebar"
        className={`fixed left-0 top-0 z-20 w-72 bg-white dark:bg-black flex flex-col h-full transform transition-transform duration-300 ease-in-out ${
          sidebarOpen ? "translate-x-0" : "-translate-x-full"
        }`}
      >
        {/* Logo - Fixed at top */}
        <div className="flex items-center justify-between h-24 px-4">
          <div className="flex items-center gap-4">
            <span className="h-10 flex-shrink-0">
              <img className="logo-abbr h-full w-auto" src={logoSmall} alt="" />
            </span>
            <span className="h-9 transition-opacity duration-300">
              <img
                className="brand-title h-full w-auto"
                src={logoText}
                alt=""
              />
            </span>
          </div>

          {/* Close button */}
          <button
            onClick={closeMobileSidebar}
            className="p-[0.4rem] flex items-center justify-center rounded-[5px] border-[1px] border-gray-200 dark:border-gray-700 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors duration-200"
            aria-label="Close sidebar"
          >
            <ArrowLeft size={22} className="text-gray-600 dark:text-gray-300" />
          </button>
        </div>

        {/* Navigation - Scrollable */}
        <nav className="flex-1 overflow-y-auto p-4 scrollbar-thin">
          <ul className="space-y-2">{menuItems.map(renderMenuItem)}</ul>

          {/* Date widget at bottom - Fixed */}
          <div className="p-4 flex-shrink-0">
            <div className="bg-[#ff6d4c] text-white p-3 relative overflow-hidden rounded-[1rem] h-26 flex items-center transition-colors duration-200">
              <div className="text-[15px] font-medium whitespace-nowrap pl-5">
                {getTodaysDate()}
              </div>
              <CalendarDays
                size={120}
                className="text-gray-200 -rotate-[20deg] absolute -right-[5%] top-[10%] opacity-[0.3]"
              />
              {/* <div className="text-xs opacity-90">localhost/kb/</div> */}
            </div>
          </div>
          <div className="text-center text-gray-500 dark:text-gray-400 pb-4 pl-5">
            <p className="text-sm font-light text-left ">
              <strong className="font-normal">Cute Profit GIS</strong> ©{" "}
              {new Date().getFullYear()} All Rights Reserved
            </p>
            <p className="text-xs font-medium text-left flex mt-4">
              Made with
              <Heart size={16} className="mx-1 text-red-500" />
              by Mine Softwares
            </p>
          </div>
        </nav>
      </aside>
    </>
  );
}

export default MobileSidebar;
