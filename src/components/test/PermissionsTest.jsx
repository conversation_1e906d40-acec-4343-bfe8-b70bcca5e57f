import { useState, useEffect } from "react";
import { permissionsService } from "../../services/permissionsService";

const PermissionsTest = () => {
  const [permissions, setPermissions] = useState([]);
  const [groupedPermissions, setGroupedPermissions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const testPermissionsGrouping = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Test fetching all permissions
      console.log("Fetching all permissions...");
      const allPermissions = await permissionsService.getAll();
      setPermissions(allPermissions);
      console.log("All permissions:", allPermissions);

      // Test grouping permissions by description
      console.log("Grouping permissions by description...");
      const grouped = await permissionsService.getPermissionsGrouped();
      setGroupedPermissions(grouped);
      console.log("Grouped permissions:", grouped);

    } catch (err) {
      console.error("Error testing permissions:", err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    testPermissionsGrouping();
  }, []);

  return (
    <div className="p-6 bg-white dark:bg-gray-800 rounded-lg shadow">
      <h2 className="text-xl font-bold mb-4 text-gray-900 dark:text-white">
        Permissions Categorization Test
      </h2>
      
      <div className="space-y-4">
        <button
          onClick={testPermissionsGrouping}
          disabled={loading}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
        >
          {loading ? "Testing..." : "Test Permissions Grouping"}
        </button>

        {error && (
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-600">Error: {error}</p>
          </div>
        )}

        {/* Raw Permissions */}
        <div className="mt-6">
          <h3 className="text-lg font-semibold mb-2 text-gray-900 dark:text-white">
            Raw Permissions ({permissions.length})
          </h3>
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg max-h-40 overflow-y-auto">
            <pre className="text-sm text-gray-700 dark:text-gray-300">
              {JSON.stringify(permissions, null, 2)}
            </pre>
          </div>
        </div>

        {/* Grouped Permissions */}
        <div className="mt-6">
          <h3 className="text-lg font-semibold mb-2 text-gray-900 dark:text-white">
            Grouped by Description ({groupedPermissions.length} categories)
          </h3>
          
          {groupedPermissions.map((category, index) => (
            <div key={index} className="mb-4 border border-gray-200 dark:border-gray-600 rounded-lg">
              <div className="p-3 bg-gray-100 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600">
                <h4 className="font-medium text-gray-900 dark:text-white">
                  {category.name} ({category.permissions.length} permissions)
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  ID: {category.id} | Description: {category.description}
                </p>
              </div>
              <div className="p-3">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  {category.permissions.map((permission) => (
                    <div
                      key={permission.id}
                      className="p-2 bg-blue-50 dark:bg-blue-900/20 rounded border"
                    >
                      <div className="font-medium text-sm text-gray-900 dark:text-white">
                        {permission.name}
                      </div>
                      <div className="text-xs text-gray-600 dark:text-gray-400">
                        {permission.action} • {permission.resource}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Example Data Structure */}
        <div className="mt-6">
          <h3 className="text-lg font-semibold mb-2 text-gray-900 dark:text-white">
            Expected Permission Data Structure
          </h3>
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <pre className="text-sm text-gray-700 dark:text-gray-300">
{`{
  "data": [
    {
      "id": "1",
      "name": "View Items",
      "description": "items",
      "action": "view",
      "resource": "items"
    },
    {
      "id": "2",
      "name": "Create Items", 
      "description": "items",
      "action": "create",
      "resource": "items"
    },
    {
      "id": "3",
      "name": "View Users",
      "description": "administration",
      "action": "view", 
      "resource": "users"
    }
  ]
}`}
            </pre>
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
            This creates two categories: "Items Management" and "Administration Management"
          </p>
        </div>
      </div>
    </div>
  );
};

export default PermissionsTest;
