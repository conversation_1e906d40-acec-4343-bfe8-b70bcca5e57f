import React, { useState } from 'react';
import { downloadLeadsTemplate } from '../../utils/excelUtils';
import { toast } from 'react-toastify';

const ImportDemo = () => {
  const [selectedFile, setSelectedFile] = useState(null);

  const handleDownloadTemplate = () => {
    try {
      downloadLeadsTemplate();
      toast.success("Template downloaded successfully!");
    } catch (error) {
      console.error("Error downloading template:", error);
      toast.error("Failed to download template");
    }
  };

  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    setSelectedFile(file);
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h2 className="text-2xl font-bold mb-6">Import Leads Demo</h2>
      
      {/* Download Template Section */}
      <div className="mb-6 p-4 border rounded-lg">
        <h3 className="text-lg font-semibold mb-2">Step 1: Download Template</h3>
        <p className="text-gray-600 mb-4">
          Download the Excel template to see the required format for importing leads.
        </p>
        <button
          onClick={handleDownloadTemplate}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          Download Template
        </button>
      </div>

      {/* File Selection Section */}
      <div className="mb-6 p-4 border rounded-lg">
        <h3 className="text-lg font-semibold mb-2">Step 2: Select File</h3>
        <p className="text-gray-600 mb-4">
          Select an Excel (.xlsx, .xls) or CSV file. File parsing is now handled by the backend.
        </p>
        <input
          type="file"
          accept=".xlsx,.xls,.csv"
          onChange={handleFileSelect}
          className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
        />
        {selectedFile && (
          <p className="mt-2 text-sm text-gray-600">
            Selected: {selectedFile.name} ({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)
          </p>
        )}
      </div>

      {/* Backend Processing Note */}
      <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
        <h3 className="text-lg font-semibold mb-2">Backend Processing</h3>
        <p className="text-gray-700">
          File parsing and validation is now handled by the backend at <code>/api/leads/upload-excel</code>.
          The frontend sends the raw file and anchor ID via FormData.
        </p>
      </div>

      {/* Instructions */}
      <div className="mt-6 p-4 bg-blue-50 rounded-lg">
        <h3 className="text-lg font-semibold mb-2">Refactored Import Process</h3>
        <ul className="list-disc list-inside space-y-1 text-sm text-gray-700">
          <li><strong>Frontend:</strong> Template generation, file selection, anchor selection</li>
          <li><strong>Backend:</strong> File parsing, validation, data processing</li>
          <li><strong>API Endpoint:</strong> POST /api/leads/upload-excel (FormData with file + anchorId)</li>
          <li><strong>Response:</strong> JSON with success/error and count of imported leads</li>
          <li><strong>Benefits:</strong> Better security, server-side validation, reduced client load</li>
          <li><strong>File Types:</strong> Excel (.xlsx, .xls) and CSV files supported</li>
          <li><strong>Anchor Assignment:</strong> Selected anchor applied to all imported leads</li>
        </ul>
      </div>
    </div>
  );
};

export default ImportDemo;
