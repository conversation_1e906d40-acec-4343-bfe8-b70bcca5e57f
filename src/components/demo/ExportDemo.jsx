import React, { useState, useEffect } from 'react';
import { Download, Keyboard, Clock, FileSpreadsheet, CheckCircle } from 'lucide-react';
import { getExportHistory, clearExportHistory, getExportFormats } from '../../utils/excelUtils';
import { useDataTableShortcuts } from '../../hooks/useKeyboardShortcuts';
import ExportModal from '../modals/ExportModal';

const ExportDemo = () => {
  const [isExportModalOpen, setIsExportModalOpen] = useState(false);
  const [exportHistory, setExportHistory] = useState([]);
  const [keyPressed, setKeyPressed] = useState('');

  const exportFormats = getExportFormats();

  // Load export history
  useEffect(() => {
    setExportHistory(getExportHistory());
  }, []);

  // Keyboard shortcuts
  useDataTableShortcuts({
    onExport: () => {
      setIsExportModalOpen(true);
      setKeyPressed('Ctrl+E pressed!');
      setTimeout(() => setKeyPressed(''), 2000);
    },
  });

  const handleClearHistory = () => {
    clearExportHistory();
    setExportHistory([]);
  };

  const formatTimestamp = (timestamp) => {
    return new Date(timestamp).toLocaleString();
  };

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <h1 className="text-3xl font-bold mb-6 text-gray-900 dark:text-white">
        Export Leads Feature Demo
      </h1>

      {/* Keyboard Shortcut Indicator */}
      {keyPressed && (
        <div className="mb-6 p-4 bg-green-100 border border-green-400 text-green-700 rounded-lg flex items-center">
          <CheckCircle size={20} className="mr-2" />
          {keyPressed}
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Features Overview */}
        <div className="space-y-6">
          {/* Core Features */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold mb-4 flex items-center">
              <FileSpreadsheet className="mr-2 text-blue-600" size={24} />
              Core Features
            </h2>
            <ul className="space-y-3">
              <li className="flex items-start">
                <CheckCircle className="mr-2 text-green-500 mt-0.5" size={16} />
                <div>
                  <strong>Excel Export:</strong> Export leads to properly formatted Excel files
                </div>
              </li>
              <li className="flex items-start">
                <CheckCircle className="mr-2 text-green-500 mt-0.5" size={16} />
                <div>
                  <strong>Search Filtering:</strong> Filter exported data with search queries
                </div>
              </li>
              <li className="flex items-start">
                <CheckCircle className="mr-2 text-green-500 mt-0.5" size={16} />
                <div>
                  <strong>Progress Tracking:</strong> Real-time progress indication for large exports
                </div>
              </li>
              <li className="flex items-start">
                <CheckCircle className="mr-2 text-green-500 mt-0.5" size={16} />
                <div>
                  <strong>Export History:</strong> Track and manage recent downloads
                </div>
              </li>
              <li className="flex items-start">
                <CheckCircle className="mr-2 text-green-500 mt-0.5" size={16} />
                <div>
                  <strong>Error Handling:</strong> Comprehensive error handling with user feedback
                </div>
              </li>
            </ul>
          </div>

          {/* Keyboard Shortcuts */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold mb-4 flex items-center">
              <Keyboard className="mr-2 text-purple-600" size={24} />
              Keyboard Shortcuts
            </h2>
            <div className="space-y-2">
              <div className="flex justify-between items-center p-2 bg-gray-50 dark:bg-gray-700 rounded">
                <span>Export Leads</span>
                <kbd className="px-2 py-1 bg-gray-200 dark:bg-gray-600 rounded text-sm">Ctrl+E</kbd>
              </div>
              <div className="flex justify-between items-center p-2 bg-gray-50 dark:bg-gray-700 rounded">
                <span>Import Leads</span>
                <kbd className="px-2 py-1 bg-gray-200 dark:bg-gray-600 rounded text-sm">Ctrl+I</kbd>
              </div>
              <div className="flex justify-between items-center p-2 bg-gray-50 dark:bg-gray-700 rounded">
                <span>New Lead</span>
                <kbd className="px-2 py-1 bg-gray-200 dark:bg-gray-600 rounded text-sm">Ctrl+N</kbd>
              </div>
            </div>
            <p className="mt-3 text-sm text-gray-600 dark:text-gray-400">
              Try pressing <kbd className="px-1 py-0.5 bg-gray-200 dark:bg-gray-600 rounded text-xs">Ctrl+E</kbd> to test the export shortcut!
            </p>
          </div>

          {/* Export Formats */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold mb-4">Export Formats</h2>
            <div className="grid grid-cols-1 gap-3">
              {exportFormats.map((format) => (
                <div
                  key={format.value}
                  className="flex items-center p-3 border border-gray-200 dark:border-gray-600 rounded-lg"
                >
                  <span className="text-2xl mr-3">{format.icon}</span>
                  <div>
                    <div className="font-medium">{format.label}</div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {format.value === 'xlsx' && 'Recommended for data analysis'}
                      {format.value === 'csv' && 'Compatible with all spreadsheet apps'}
                      {format.value === 'pdf' && 'Perfect for reports and printing'}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Demo Actions & History */}
        <div className="space-y-6">
          {/* Demo Actions */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold mb-4">Try It Out</h2>
            <div className="space-y-4">
              <button
                onClick={() => setIsExportModalOpen(true)}
                className="w-full flex items-center justify-center px-4 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors duration-200"
              >
                <Download size={20} className="mr-2" />
                Open Export Modal
              </button>
              
              <div className="text-sm text-gray-600 dark:text-gray-400">
                <p className="mb-2">This will open the export modal where you can:</p>
                <ul className="list-disc list-inside space-y-1 ml-4">
                  <li>Set search filters</li>
                  <li>Choose export format</li>
                  <li>View progress indication</li>
                  <li>See export history</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Export History */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold flex items-center">
                <Clock className="mr-2 text-orange-600" size={24} />
                Export History
              </h2>
              {exportHistory.length > 0 && (
                <button
                  onClick={handleClearHistory}
                  className="text-sm text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                >
                  Clear All
                </button>
              )}
            </div>
            
            {exportHistory.length > 0 ? (
              <div className="space-y-3 max-h-64 overflow-y-auto">
                {exportHistory.map((entry) => (
                  <div
                    key={entry.id}
                    className="p-3 border border-gray-200 dark:border-gray-600 rounded-lg"
                  >
                    <div className="font-medium text-sm truncate">
                      {entry.filename}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      {formatTimestamp(entry.timestamp)}
                    </div>
                    {entry.searchQuery && (
                      <div className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                        Filter: {entry.searchQuery}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                <Clock size={48} className="mx-auto mb-2 opacity-50" />
                <p>No export history yet</p>
                <p className="text-sm">Try exporting some data to see history here</p>
              </div>
            )}
          </div>

          {/* Technical Details */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold mb-4">Technical Implementation</h2>
            <div className="space-y-3 text-sm">
              <div>
                <strong>API Endpoint:</strong>
                <code className="ml-2 px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded">
                  GET /api/v1/leads/export
                </code>
              </div>
              <div>
                <strong>Response Type:</strong>
                <span className="ml-2">Binary Excel file with proper headers</span>
              </div>
              <div>
                <strong>Progress Tracking:</strong>
                <span className="ml-2">Real-time progress callbacks</span>
              </div>
              <div>
                <strong>Error Handling:</strong>
                <span className="ml-2">Comprehensive error messages and recovery</span>
              </div>
              <div>
                <strong>History Storage:</strong>
                <span className="ml-2">localStorage with 10-entry limit</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Export Modal */}
      <ExportModal
        isOpen={isExportModalOpen}
        onClose={() => setIsExportModalOpen(false)}
        title="Demo Export Modal"
        currentSearchQuery="demo search query"
      />
    </div>
  );
};

export default ExportDemo;
