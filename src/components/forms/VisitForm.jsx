import { useState, useEffect } from "react";
import { Upload, X } from "lucide-react";
import Select from "react-select";
import {
  purposesService,
  formatPurposesResponse,
} from "../../services/purposesService";
import instance from "../../axios/instance";

const VisitForm = ({ item, onClose, onSubmit }) => {
  const [formData, setFormData] = useState({
    visitType: "",
    visitStatus: "",
    purposeId: "",
    notes: "",
    followUpDate: "",
    followUpTime: "",
    attachments: [],
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Purposes state
  const [purposes, setPurposes] = useState([]);
  const [purposesLoading, setPurposesLoading] = useState(false);

  // Fetch purposes on component mount
  const fetchPurposes = async () => {
    try {
      setPurposesLoading(true);
      const response = await purposesService.getAll();
      const formattedPurposes = formatPurposesResponse(response);

      // Convert to React Select format
      const purposeOptions = formattedPurposes.map((purpose) => ({
        value: purpose.id,
        label: purpose.name,
        purpose: purpose,
      }));

      setPurposes(purposeOptions);
    } catch (error) {
      console.error("Error fetching purposes:", error);
      setPurposes([]);
    } finally {
      setPurposesLoading(false);
    }
  };

  useEffect(() => {
    fetchPurposes();
  }, []);

  useEffect(() => {
    if (item) {
      setFormData({
        visitType: item.visitType || "",
        visitStatus: item.visitStatus || "",
        purposeId: item.purposeId || "",
        notes: item.notes || "",
        followUpDate: item.followUpDate || "",
        followUpTime: item.followUpTime || "",
        attachments: item.attachments || [],
      });
    }
  }, [item]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    if (errors[name]) {
      setErrors((prev) => ({ ...prev, [name]: "" }));
    }
  };

  // Handle React Select changes
  const handleSelectChange = (selectedOption, actionMeta) => {
    const { name } = actionMeta;
    const value = selectedOption ? selectedOption.value : "";

    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // Clear error when user makes selection
    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  // Handle file upload
  const handleFileUpload = (e) => {
    const files = Array.from(e.target.files);
    const newAttachments = files.map((file) => ({
      id: Date.now() + Math.random(), // Temporary ID
      file: file,
      name: file.name,
      size: file.size,
      type: file.type,
    }));

    setFormData((prev) => ({
      ...prev,
      attachments: [...prev.attachments, ...newAttachments],
    }));

    // Clear the input
    e.target.value = "";
  };

  // Remove attachment
  const removeAttachment = (attachmentId) => {
    setFormData((prev) => ({
      ...prev,
      attachments: prev.attachments.filter((att) => att.id !== attachmentId),
    }));
  };

  const validateForm = () => {
    const newErrors = {};
    if (!formData.visitType) newErrors.visitType = "Visit type is required";
    if (!formData.visitStatus)
      newErrors.visitStatus = "Visit status is required";
    if (!formData.purposeId) newErrors.purposeId = "Purpose is required";
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validateForm()) return;

    setIsSubmitting(true);
    try {
      // Combine date and time into a single datetime
      let followUpDateTime = null;
      if (formData.followUpDate && formData.followUpTime) {
        // Create datetime string in ISO format
        const dateTimeString = `${formData.followUpDate}T${formData.followUpTime}:00.000Z`;
        followUpDateTime = dateTimeString;
      } else if (formData.followUpDate) {
        // If only date is provided, set time to current time
        const currentTime = new Date().toTimeString().slice(0, 8);
        const dateTimeString = `${formData.followUpDate}T${currentTime}.000Z`;
        followUpDateTime = dateTimeString;
      }

      // Create the data structure expected by backend
      const apiData = {
        visit_type: formData.visitType,
        visit_status: formData.visitStatus,
        purpose_id: formData.purposeId,
        notes: formData.notes,
        leadID: item?.id, // Include the lead ID
        ...(followUpDateTime && { follow_up_date: followUpDateTime }),
        attachments: [], // Always include attachments array, even if empty
      };

      // Create FormData for file uploads
      const formDataToSend = new FormData();

      // Add each field individually to FormData
      formDataToSend.append("visit_type", formData.visitType);
      formDataToSend.append("visit_status", formData.visitStatus);
      formDataToSend.append("purpose_id", formData.purposeId);
      formDataToSend.append("notes", formData.notes);
      formDataToSend.append("leadID", item?.id || "");

      // Add follow-up date if provided
      if (followUpDateTime) {
        formDataToSend.append("follow_up_date", followUpDateTime);
      }

      // Add actual files to FormData
      formData.attachments.forEach((attachment, index) => {
        formDataToSend.append(`attachments`, attachment.file);
      });

      // Console log the prepared data
      console.log("=== VISIT FORM SUBMISSION DATA ===");
      console.log("Backend expects individual FormData fields (not JSON)");
      console.log("FormData being sent:");
      console.log("- visit_type:", formData.visitType);
      console.log("- visit_status:", formData.visitStatus);
      console.log("- purpose_id:", formData.purposeId);
      console.log("- notes:", formData.notes);
      console.log("- leadID:", item?.id);
      console.log("- follow_up_date:", followUpDateTime || "not provided");
      console.log("- attachment files:", formData.attachments.length, "files");

      // Log FormData contents (for debugging)
      console.log("FormData contents:");
      for (let [key, value] of formDataToSend.entries()) {
        if (value instanceof File) {
          console.log(
            `${key}: [File] ${value.name} (${value.size} bytes, ${value.type})`
          );
        } else {
          console.log(`${key}: ${value}`);
        }
      }
      console.log("=====================================");

      // Make API call to /visit-activities
      try {
        console.log("Sending data to /visit-activities endpoint...");
        const response = await instance.post(
          "/visit-activities",
          formDataToSend,
          {
            headers: {
              "Content-Type": "multipart/form-data",
            },
          }
        );

        console.log("API Response:", response.data);
        console.log("Visit activity created successfully!");

        // Call the onSubmit callback with the response data
        onSubmit?.(response.data, item);
        onClose();
      } catch (apiError) {
        console.error("API Error:", apiError);
        console.error("Error response:", apiError.response?.data);

        // Still call onSubmit for now, but with error info
        onSubmit?.(formDataToSend, item, apiError);

        // You might want to show an error message to the user here
        // For now, we'll still close the modal
        onClose();
      }
    } catch (err) {
      console.error("Failed to submit visit dialog form:", err);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Custom styles for React Select
  const selectStyles = {
    control: (provided, state) => ({
      ...provided,
      minHeight: "48px",
      borderColor: state.isFocused
        ? "#10b981"
        : errors[state.selectProps.name]
        ? "#ef4444"
        : "#d1d5db",
      boxShadow: "none",
      "&:hover": {
        borderColor: state.isFocused ? "#10b981" : "#9ca3af",
      },
    }),
    option: (provided, state) => ({
      ...provided,
      backgroundColor: state.isSelected
        ? "#10b981"
        : state.isFocused
        ? "#f3f4f6"
        : "white",
      color: state.isSelected ? "white" : "#374151",
    }),
    menu: (provided) => ({
      ...provided,
      zIndex: 9999,
    }),
  };

  return (
    <form onSubmit={handleSubmit} className="flex flex-col gap-4">
      {/* Visit Type */}
      <div className="flex items-center gap-4 mt-7">
        <label
          className="text-sm font-medium w-32 flex-shrink-0"
          style={{ color: "#7e7e7e" }}
        >
          Visit Type *
        </label>
        <div className="flex-1">
          <select
            name="visitType"
            value={formData.visitType}
            onChange={handleChange}
            className={`w-full px-4 py-3 border rounded-lg bg-white outline-none transition-colors duration-200 ${
              errors.visitType
                ? "border-red-500"
                : "border-gray-300 focus:border-green-500 hover:border-gray-400"
            }`}
            style={{ color: "#7e7e7e" }}
          >
            <option value="">--select--</option>
            <option value="First Visit">First Visit</option>
            <option value="Follow Up">Follow Up</option>
          </select>
          {errors.visitType && (
            <p className="mt-1 text-sm text-red-600">{errors.visitType}</p>
          )}
        </div>
      </div>

      {/* Visit Status */}
      <div className="flex items-center gap-4">
        <label
          className="text-sm font-medium w-32 flex-shrink-0"
          style={{ color: "#7e7e7e" }}
        >
          Visit Status *
        </label>
        <div className="flex-1">
          <select
            name="visitStatus"
            value={formData.visitStatus}
            onChange={handleChange}
            className={`w-full px-4 py-3 border rounded-lg bg-white outline-none transition-colors duration-200 ${
              errors.visitStatus
                ? "border-red-500"
                : "border-gray-300 focus:border-green-500 hover:border-gray-400"
            }`}
            style={{ color: "#7e7e7e" }}
          >
            <option value="">--select--</option>
            <option value="Successful">Successful</option>
            <option value="Declined">Declined</option>
          </select>
          {errors.visitStatus && (
            <p className="mt-1 text-sm text-red-600">{errors.visitStatus}</p>
          )}
        </div>
      </div>

      {/* Purpose */}
      <div className="flex items-center gap-4">
        <label
          className="text-sm font-medium w-32 flex-shrink-0"
          style={{ color: "#7e7e7e" }}
        >
          Purpose *
        </label>
        <div className="flex-1">
          <Select
            name="purposeId"
            value={purposes.find(
              (option) => option.value === formData.purposeId
            )}
            onChange={handleSelectChange}
            options={purposes}
            styles={selectStyles}
            placeholder={
              purposesLoading ? "Loading purposes..." : "Select purpose"
            }
            isSearchable
            isLoading={purposesLoading}
            isDisabled={purposesLoading}
            className="react-select-container"
            classNamePrefix="react-select"
          />
          {errors.purposeId && (
            <p className="mt-1 text-sm text-red-600">{errors.purposeId}</p>
          )}
        </div>
      </div>

      {/* Notes */}
      <div className="flex gap-4">
        <label
          className="text-sm font-medium w-32 flex-shrink-0 pt-3"
          style={{ color: "#7e7e7e" }}
        >
          Notes
        </label>
        <div className="flex-1">
          <textarea
            name="notes"
            value={formData.notes}
            onChange={handleChange}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg bg-white outline-none transition-colors duration-200 focus:border-green-500 hover:border-gray-400 resize-none"
            style={{ color: "#7e7e7e" }}
            placeholder="Add your notes..."
            rows="4"
          />
        </div>
      </div>

      {/* Follow-up Date & Time */}
      <div className="flex items-center gap-4">
        <label
          className="text-sm font-medium w-32 flex-shrink-0"
          style={{ color: "#7e7e7e" }}
        >
          Follow up date
        </label>
        <div className="flex-1 flex gap-3">
          <input
            type="date"
            name="followUpDate"
            value={formData.followUpDate}
            onChange={handleChange}
            className="flex-1 px-4 py-3 border border-gray-300 rounded-lg bg-white outline-none transition-colors duration-200 focus:border-green-500 hover:border-gray-400"
            style={{ color: "#7e7e7e" }}
          />
          <input
            type="time"
            name="followUpTime"
            value={formData.followUpTime}
            onChange={handleChange}
            className="flex-1 px-4 py-3 border border-gray-300 rounded-lg bg-white outline-none transition-colors duration-200 focus:border-green-500 hover:border-gray-400"
            style={{ color: "#7e7e7e" }}
          />
        </div>
      </div>

      {/* Attachments */}
      <div className="flex gap-4">
        <label
          className="text-sm font-medium w-32 flex-shrink-0 pt-3"
          style={{ color: "#7e7e7e" }}
        >
          Attachments
        </label>
        <div className="flex-1">
          {/* File Upload Area */}
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-gray-400 transition-colors">
            <input
              type="file"
              multiple
              onChange={handleFileUpload}
              className="hidden"
              id="file-upload-visit"
              accept="*/*"
            />
            <label
              htmlFor="file-upload-visit"
              className="cursor-pointer flex flex-col items-center gap-2"
            >
              <Upload size={24} style={{ color: "#7e7e7e" }} />
              <span className="text-sm" style={{ color: "#7e7e7e" }}>
                Click to upload files or drag and drop
              </span>
              <span className="text-xs" style={{ color: "#9ca3af" }}>
                Any file format supported
              </span>
            </label>
          </div>

          {/* Uploaded Files List */}
          {formData.attachments.length > 0 && (
            <div className="mt-3 space-y-2">
              {formData.attachments.map((attachment) => (
                <div
                  key={attachment.id}
                  className="flex items-center justify-between p-2 bg-gray-50 rounded-lg border"
                >
                  <div className="flex items-center gap-2">
                    <div className="text-sm" style={{ color: "#7e7e7e" }}>
                      <div className="font-medium">{attachment.name}</div>
                      <div className="text-xs" style={{ color: "#9ca3af" }}>
                        {(attachment.size / 1024).toFixed(1)} KB •{" "}
                        {attachment.type || "Unknown type"}
                      </div>
                    </div>
                  </div>
                  <button
                    type="button"
                    onClick={() => removeAttachment(attachment.id)}
                    className="p-1 text-red-500 hover:text-red-700 transition-colors"
                  >
                    <X size={16} />
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Buttons */}
      <div className="flex justify-end gap-3 pt-5 mt-5">
        <button
          type="button"
          onClick={onClose}
          className="px-7 py-3 text-sm font-medium rounded-lg transition-colors"
          style={{
            color: "#f46b68",
            backgroundColor: "#fef7f7",
          }}
          onMouseEnter={(e) => {
            e.target.style.backgroundColor = "#f46b68";
            e.target.style.color = "white";
          }}
          onMouseLeave={(e) => {
            e.target.style.backgroundColor = "#fef7f7";
            e.target.style.color = "#f46b68";
          }}
        >
          Close
        </button>
        <button
          type="submit"
          disabled={isSubmitting}
          className="px-7 py-3 text-white text-sm font-medium rounded-lg disabled:opacity-50 transition-colors"
          style={{ backgroundColor: "#1c5b41" }}
          onMouseEnter={(e) => {
            if (!isSubmitting) {
              e.target.style.backgroundColor = "#164a35";
            }
          }}
          onMouseLeave={(e) => {
            if (!isSubmitting) {
              e.target.style.backgroundColor = "#1c5b41";
            }
          }}
        >
          {isSubmitting ? "Saving..." : "Submit"}
        </button>
      </div>
    </form>
  );
};

export default VisitForm;
