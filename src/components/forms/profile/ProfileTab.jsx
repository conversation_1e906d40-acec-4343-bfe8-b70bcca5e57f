import { Minus } from "lucide-react";

const ProfileTab = ({ item, leadDetails, onClose, formatValue }) => {
  // Use leadDetails if available, fallback to item
  const data = leadDetails || item;

  // Get first contact person if available
  const firstContactPerson =
    data.contact_persons && data.contact_persons.length > 0
      ? data.contact_persons[0]
      : null;

  return (
    <div className="px-6 -mx-6">
      {/* HIT Section */}
      <div className="mb-0 pl-[7%]">
        <div className="flex gap-[26%] items-center py-4">
          <span className="text-lg font-semibold w-[50px]">HIT</span>
          <span className="text-lg font-semibold">
            {data.customer_name || data.lead_name || data.name || (
              <Minus size={16} className="text-gray-400" />
            )}
          </span>
        </div>
      </div>

      {/* Profile Details - Single Column Layout */}
      <div className="space-y-0 pl-[7%]">
        {/* Lead Type */}
        <div className="flex gap-[26%] items-center py-4 border-b border-gray-100">
          <span
            className="font-semibold w-[50px] whitespace-nowrap"
            style={{ color: "#7e7e7e" }}
          >
            Lead Type
          </span>
          <span style={{ color: "#7e7e7e" }}>
            {formatValue(data.type_of_lead)}
          </span>
        </div>

        {/* Mobile */}
        <div className="flex gap-[26%] items-center py-4 border-b border-gray-100">
          <span
            className="font-semibold w-[50px] whitespace-nowrap"
            style={{ color: "#7e7e7e" }}
          >
            Mobile
          </span>
          <span style={{ color: "#7e7e7e" }}>
            {formatValue(data.phone_number || data.phoneNumber)}
          </span>
        </div>

        {/* Client ID */}
        <div className="flex gap-[26%] items-center py-4 border-b border-gray-100">
          <span
            className="font-semibold w-[50px] whitespace-nowrap"
            style={{ color: "#7e7e7e" }}
          >
            Client ID
          </span>
          <span style={{ color: "#7e7e7e" }}>
            {formatValue(data.client_id)}
          </span>
        </div>

        {/* Customer Category */}
        <div className="flex gap-[26%] items-center py-4 border-b border-gray-100">
          <span
            className="font-semibold w-[50px] whitespace-nowrap"
            style={{ color: "#7e7e7e" }}
          >
            Category
          </span>
          <span style={{ color: "#7e7e7e" }}>
            {formatValue(data.customer_category?.name)}
          </span>
        </div>

        {/* Branch */}
        <div className="flex gap-[26%] items-center py-4 border-b border-gray-100">
          <span
            className="font-semibold w-[50px] whitespace-nowrap"
            style={{ color: "#7e7e7e" }}
          >
            Branch
          </span>
          <span style={{ color: "#7e7e7e" }}>
            {formatValue(data.branch?.name)}
          </span>
        </div>

        {/* Added by */}
        <div className="flex gap-[26%] items-center py-4 border-b border-gray-100">
          <span
            className="font-semibold w-[50px] whitespace-nowrap"
            style={{ color: "#7e7e7e" }}
          >
            Added by
          </span>
          <span style={{ color: "#7e7e7e" }}>
            {formatValue(data.rm_user?.name)}
          </span>
        </div>

        {/* Sector */}
        <div className="flex gap-[26%] items-center py-4 border-b border-gray-100">
          <span
            className="font-semibold w-[50px] whitespace-nowrap"
            style={{ color: "#7e7e7e" }}
          >
            Sector
          </span>
          <span style={{ color: "#7e7e7e" }}>
            {formatValue(data.isic_sector?.name)}
          </span>
        </div>

        {/* Region */}
        <div className="flex gap-[26%] items-center py-4 border-b border-gray-100">
          <span
            className="font-semibold w-[50px] whitespace-nowrap"
            style={{ color: "#7e7e7e" }}
          >
            Region
          </span>
          <span style={{ color: "#7e7e7e" }}>
            {formatValue(data.branch?.region?.name)}
          </span>
        </div>

        {/* Contact Person */}
        <div className="flex gap-[26%] items-center py-4 border-b border-gray-100">
          <span
            className="font-semibold w-[50px] whitespace-nowrap"
            style={{ color: "#7e7e7e" }}
          >
            Contact Person
          </span>
          <span style={{ color: "#7e7e7e" }}>
            {formatValue(firstContactPerson?.name)}
          </span>
        </div>

        {/* Contact Phone */}
        <div className="flex gap-[26%] items-center py-4">
          <span
            className="font-semibold w-[50px] whitespace-nowrap"
            style={{ color: "#7e7e7e" }}
          >
            Contact Phone
          </span>
          <span style={{ color: "#7e7e7e" }}>
            {formatValue(firstContactPerson?.phone_number)}
          </span>
        </div>
      </div>

      {/* Close Button */}
      <div className="flex justify-end pt-8 pb-6 pr-[10%]">
        <button
          onClick={onClose}
          className="px-7 py-3 text-sm font-medium rounded-lg transition-colors"
          style={{
            color: "#f46b68",
            backgroundColor: "#fef7f7",
          }}
          onMouseEnter={(e) => {
            e.target.style.backgroundColor = "#f46b68";
            e.target.style.color = "white";
          }}
          onMouseLeave={(e) => {
            e.target.style.backgroundColor = "#fef7f7";
            e.target.style.color = "#f46b68";
          }}
        >
          Close
        </button>
      </div>
    </div>
  );
};

export default ProfileTab;
