import { useState, useEffect } from "react";

const FollowUpForm = ({ item, onClose, onSubmit }) => {
  const [formData, setFormData] = useState({
    clientName: "",
    anchorName: "",
    assignedOfficer: "",
    followUpType: "Phone Call",
    followUpReason: "",
    date: "",
    status: "Pending",
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Available follow-up types
  const followUpTypes = ["Phone Call", "Site Visit"];

  // Available officers (in a real app, this would come from an API)
  const officers = [
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
  ];

  // Available statuses
  const statuses = ["Pending", "Completed", "Canceled"];

  // Populate form with item data for editing/rescheduling
  useEffect(() => {
    if (item) {
      setFormData({
        clientName: item.clientName || "",
        anchorName: item.anchorName || "",
        assignedOfficer: item.assignedOfficer || "",
        followUpType: item.followUpType || "Phone Call",
        followUpReason: item.followUpReason || "",
        date: item.date ? new Date(item.date).toISOString().slice(0, 16) : "",
        status: item.status || "Pending",
      });
    }
  }, [item]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.clientName.trim()) {
      newErrors.clientName = "Client name is required";
    }

    if (!formData.assignedOfficer.trim()) {
      newErrors.assignedOfficer = "Assigned officer is required";
    }

    if (!formData.followUpReason.trim()) {
      newErrors.followUpReason = "Follow-up reason is required";
    }

    if (!formData.date) {
      newErrors.date = "Date is required";
    } else {
      const selectedDate = new Date(formData.date);
      const now = new Date();
      if (selectedDate < now) {
        newErrors.date = "Date cannot be in the past";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const submitData = {
        ...formData,
        id: item?.id,
        createdDate: item?.createdDate || new Date().toISOString(),
      };

      await onSubmit(submitData);
      onClose();
    } catch (error) {
      console.error("Error submitting form:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Client Name */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Client Name *
        </label>
        <input
          type="text"
          name="clientName"
          value={formData.clientName}
          onChange={handleInputChange}
          className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 outline-none transition-colors duration-200 ${
            errors.clientName
              ? "border-red-500 dark:border-red-400"
              : "border-gray-300 dark:border-gray-600 focus:border-green-500 hover:border-gray-400"
          }`}
          placeholder="Enter client name"
        />
        {errors.clientName && (
          <p className="mt-1 text-sm text-red-600 dark:text-red-400">
            {errors.clientName}
          </p>
        )}
      </div>

      {/* Anchor Name (Optional) */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Anchor Name (Optional)
        </label>
        <input
          type="text"
          name="anchorName"
          value={formData.anchorName}
          onChange={handleInputChange}
          className="w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 outline-none transition-colors duration-200 border-gray-300 dark:border-gray-600 focus:border-green-500 hover:border-gray-400"
          placeholder="Enter anchor name"
        />
      </div>

      {/* Assigned Officer */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Assigned Officer *
        </label>
        <select
          name="assignedOfficer"
          value={formData.assignedOfficer}
          onChange={handleInputChange}
          className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white outline-none transition-colors duration-200 ${
            errors.assignedOfficer
              ? "border-red-500 dark:border-red-400"
              : "border-gray-300 dark:border-gray-600 focus:border-green-500 hover:border-gray-400"
          }`}
        >
          <option value="">Select an officer</option>
          {officers.map((officer) => (
            <option key={officer} value={officer}>
              {officer}
            </option>
          ))}
        </select>
        {errors.assignedOfficer && (
          <p className="mt-1 text-sm text-red-600 dark:text-red-400">
            {errors.assignedOfficer}
          </p>
        )}
      </div>

      {/* Follow-up Type */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Follow-up Type *
        </label>
        <select
          name="followUpType"
          value={formData.followUpType}
          onChange={handleInputChange}
          className="w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white outline-none transition-colors duration-200 border-gray-300 dark:border-gray-600 focus:border-green-500 hover:border-gray-400"
        >
          {followUpTypes.map((type) => (
            <option key={type} value={type}>
              {type}
            </option>
          ))}
        </select>
      </div>

      {/* Follow-up Reason */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Follow-up Reason *
        </label>
        <textarea
          name="followUpReason"
          value={formData.followUpReason}
          onChange={handleInputChange}
          rows={3}
          className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 outline-none transition-colors duration-200 ${
            errors.followUpReason
              ? "border-red-500 dark:border-red-400"
              : "border-gray-300 dark:border-gray-600 focus:border-green-500 hover:border-gray-400"
          }`}
          placeholder="Enter the reason for this follow-up"
        />
        {errors.followUpReason && (
          <p className="mt-1 text-sm text-red-600 dark:text-red-400">
            {errors.followUpReason}
          </p>
        )}
      </div>

      {/* Date */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Date *
        </label>
        <input
          type="datetime-local"
          name="date"
          value={formData.date}
          onChange={handleInputChange}
          className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white outline-none transition-colors duration-200 ${
            errors.date
              ? "border-red-500 dark:border-red-400"
              : "border-gray-300 dark:border-gray-600 focus:border-green-500 hover:border-gray-400"
          }`}
        />
        {errors.date && (
          <p className="mt-1 text-sm text-red-600 dark:text-red-400">
            {errors.date}
          </p>
        )}
      </div>

      {/* Status (only show for editing) */}
      {item && (
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Status
          </label>
          <select
            name="status"
            value={formData.status}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          >
            {statuses.map((status) => (
              <option key={status} value={status}>
                {status}
              </option>
            ))}
          </select>
        </div>
      )}

      {/* Form Actions */}
      <div className="flex justify-end space-x-3 pt-4">
        <button
          type="button"
          onClick={onClose}
          className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-lg hover:bg-gray-200 focus:ring-2 focus:ring-gray-500 dark:bg-gray-600 dark:text-gray-300 dark:border-gray-500 dark:hover:bg-gray-700"
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={isSubmitting}
          className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isSubmitting
            ? "Saving..."
            : item
            ? "Update Follow-up"
            : "Create Follow-up"}
        </button>
      </div>
    </form>
  );
};

export default FollowUpForm;
