import { useState, useEffect } from "react";

//NOTE: if an item is passed in, it's an edit form
const AnchorForm = ({ item, onClose, onSubmit }) => {
  const [formData, setFormData] = useState({
    anchorID: "",
    name: "",
    type: "Retail Store",
    location: "",
    email: "",
    mobile: "",
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Populate form with item data for editing
  useEffect(() => {
    if (item) {
      setFormData({
        anchorID: item.anchorID || "",
        name: item.name || "",
        type: item.type || "Retail Store",
        location: item.location || "",
        email: item.email || "",
        mobile: item.mobile || "",
      });
    }
  }, [item]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = "Anchor name is required";
    }

    if (!formData.type.trim()) {
      newErrors.type = "Anchor type is required";
    }

    if (!formData.location.trim()) {
      newErrors.location = "Location is required";
    }

    if (!formData.email.trim()) {
      newErrors.email = "Email is required";
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Email is invalid";
    }

    if (!formData.mobile.trim()) {
      newErrors.mobile = "Mobile number is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Prepare data for API integration
      const apiData = {
        ...formData,
        // Add timestamps and metadata for API
        ...(item ? {} : { createdAt: new Date().toISOString() }),
        ...(item ? { updatedAt: new Date().toISOString() } : {}),
        // Remove anchorID for creation (will be generated by backend)
        ...(item ? {} : { anchorID: undefined }),
      };

      console.log("API data prepared:", apiData);

      // API Integration: Create or Update anchor
      // Uncomment when API is ready
      // const endpoint = item
      //   ? `/api/anchors/${item.id}`
      //   : '/api/anchors';
      // const method = item ? 'PUT' : 'POST';

      // const response = await fetch(endpoint, {
      //   method: method,
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(apiData)
      // });

      // if (!response.ok) {
      //   throw new Error(`Failed to ${item ? 'update' : 'create'} anchor`);
      // }

      // const result = await response.json();
      // console.log(`Anchor ${item ? 'updated' : 'created'} successfully:`, result);

      // For now, simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      if (onSubmit) {
        onSubmit(apiData);
      }

      console.log(item ? "Updated anchor:" : "Created anchor:", apiData);
      onClose();
    } catch (error) {
      console.error("Error submitting anchor form:", error);
      // Here you would typically show an error notification to the user
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Anchor ID - only show for editing */}
        {item && (
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Anchor ID
            </label>
            <input
              type="text"
              name="anchorID"
              value={formData.anchorID}
              disabled
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white"
            />
          </div>
        )}

        {/* Name */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Name *
          </label>
          <input
            type="text"
            name="name"
            value={formData.name}
            onChange={handleChange}
            className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 outline-none transition-colors duration-200 ${
              errors.name
                ? "border-red-500 dark:border-red-400"
                : "border-gray-300 dark:border-gray-600 focus:border-green-500 hover:border-gray-400"
            }`}
            placeholder="Enter anchor name"
          />
          {errors.name && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {errors.name}
            </p>
          )}
        </div>

        {/* Type */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Type *
          </label>
          <select
            name="type"
            value={formData.type}
            onChange={handleChange}
            className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white outline-none transition-colors duration-200 ${
              errors.type
                ? "border-red-500 dark:border-red-400"
                : "border-gray-300 dark:border-gray-600 focus:border-green-500 hover:border-gray-400"
            }`}
          >
            <option value="Retail Store">Retail Store</option>
            <option value="Manufacturing">Manufacturing</option>
            <option value="Distribution">Distribution</option>
            <option value="Food Service">Food Service</option>
            <option value="Agriculture">Agriculture</option>
          </select>
          {errors.type && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {errors.type}
            </p>
          )}
        </div>

        {/* Location */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Location *
          </label>
          <input
            type="text"
            name="location"
            value={formData.location}
            onChange={handleChange}
            className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 outline-none transition-colors duration-200 ${
              errors.location
                ? "border-red-500 dark:border-red-400"
                : "border-gray-300 dark:border-gray-600 focus:border-green-500 hover:border-gray-400"
            }`}
            placeholder="Enter physical location"
          />
          {errors.location && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {errors.location}
            </p>
          )}
        </div>

        {/* Email */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Email *
          </label>
          <input
            type="email"
            name="email"
            value={formData.email}
            onChange={handleChange}
            className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 outline-none transition-colors duration-200 ${
              errors.email
                ? "border-red-500 dark:border-red-400"
                : "border-gray-300 dark:border-gray-600 focus:border-green-500 hover:border-gray-400"
            }`}
            placeholder="Enter email address"
          />
          {errors.email && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {errors.email}
            </p>
          )}
        </div>

        {/* Mobile */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Mobile *
          </label>
          <input
            type="tel"
            name="mobile"
            value={formData.mobile}
            onChange={handleChange}
            className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 outline-none transition-colors duration-200 ${
              errors.mobile
                ? "border-red-500 dark:border-red-400"
                : "border-gray-300 dark:border-gray-600 focus:border-green-500 hover:border-gray-400"
            }`}
            placeholder="Enter mobile number"
          />
          {errors.mobile && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {errors.mobile}
            </p>
          )}
        </div>
      </div>

      {/* Form Actions */}
      <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
        <button
          type="button"
          onClick={onClose}
          className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors duration-200"
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={isSubmitting}
          className="px-4 py-2 text-sm font-medium text-white bg-[#165026] hover:bg-green-700 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isSubmitting
            ? "Saving..."
            : item
            ? "Update Anchor"
            : "Create Anchor"}
        </button>
      </div>
    </form>
  );
};

export default AnchorForm;
