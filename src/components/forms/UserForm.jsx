import { useState, useEffect, useCallback } from "react";
import Select from "react-select";
import { Eye, EyeOff, Check, X } from "lucide-react";
import { formatUserDataForForm, validateUserData } from "../../services/userService";
import { toast } from "react-toastify";

const UserForm = ({ item, onClose, onSubmit, userApi, isEdit = false }) => {
  // Form state with all required fields for backend
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone_number: "",
    role: "", // Will store role_id
    branch: "", // Will store branch_id
    rm_code: "", // New field for RM code
    password: "",
    confirmPassword: "", // Frontend validation only - never sent to backend
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // State for dropdown options loaded from backend
  const [roleOptions, setRoleOptions] = useState([]);
  const [branchOptions, setBranchOptions] = useState([]);
  const [loadingOptions, setLoadingOptions] = useState(true);

  // Helper function to get password match status for visual indicators
  const getPasswordMatchStatus = () => {
    if (!formData.password || !formData.confirmPassword) {
      return null; // No status if either field is empty
    }
    return formData.password === formData.confirmPassword ? 'match' : 'mismatch';
  };

  // Load dropdown options from backend
  const loadDropdownOptions = useCallback(async () => {
    if (!userApi) {
      console.warn('userApi not provided to UserForm');
      setLoadingOptions(false);
      return;
    }

    setLoadingOptions(true);
    try {
      // Load roles and branches in parallel using userApi
      const [roles, branches] = await Promise.all([
        userApi.getRoles(),
        userApi.getBranches()
      ]);

      setRoleOptions(roles);
      setBranchOptions(branches);
      console.log('Loaded roles:', roles);
      console.log('Loaded branches:', branches);
    } catch (error) {
      console.error('Error loading dropdown options:', error);
      // Set empty arrays on error to prevent form breaking
      setRoleOptions([]);
      setBranchOptions([]);
    } finally {
      setLoadingOptions(false);
    }
  }, [userApi]);

  // Custom styles for React Select to match the theme
  const selectStyles = {
    control: (provided, state) => ({
      ...provided,
      backgroundColor: "rgb(255 255 255)",
      borderColor: state.isFocused
        ? "rgb(34 197 94)"
        : state.hasValue && state.selectProps.error
        ? "rgb(239 68 68)"
        : "rgb(209 213 219)",
      borderWidth: "1px",
      borderRadius: "0.5rem",
      minHeight: "42px",
      boxShadow: "none",
      "&:hover": {
        borderColor: state.isFocused ? "rgb(34 197 94)" : "rgb(156 163 175)",
      },
    }),
    valueContainer: (provided) => ({
      ...provided,
      padding: "2px 12px",
    }),
    input: (provided) => ({
      ...provided,
      color: "inherit",
    }),
    singleValue: (provided) => ({
      ...provided,
      color: "inherit",
    }),
    menu: (provided) => ({
      ...provided,
      backgroundColor: "rgb(255 255 255)",
      border: "1px solid rgb(209 213 219)",
      borderRadius: "0.5rem",
      zIndex: 9999,
    }),
    option: (provided, state) => ({
      ...provided,
      backgroundColor: state.isSelected
        ? "rgb(34 197 94)"
        : state.isFocused
        ? "rgb(243 244 246)"
        : "transparent",
      color: state.isSelected ? "white" : "inherit",
      "&:hover": {
        backgroundColor: state.isSelected
          ? "rgb(34 197 94)"
          : "rgb(243 244 246)",
      },
    }),
    placeholder: (provided) => ({
      ...provided,
      color: "rgb(156 163 175)",
    }),
  };

  // Load dropdown options on component mount
  useEffect(() => {
    loadDropdownOptions();
  }, [loadDropdownOptions]);

  // Populate form with item data for editing
  useEffect(() => {
    if (item) {
      // Use the formatting function to convert backend data to form format
      const formattedData = formatUserDataForForm(item);
      setFormData(formattedData);
      console.log('Populated form with user data:', formattedData);
    }
  }, [item]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }

    // Real-time password confirmation validation
    if (name === 'password' || name === 'confirmPassword') {
      const newFormData = { ...formData, [name]: value };

      // Check if confirm password field has content and passwords don't match
      if (name === 'password' && newFormData.confirmPassword) {
        if (value !== newFormData.confirmPassword) {
          setErrors((prev) => ({
            ...prev,
            confirmPassword: "Passwords do not match",
          }));
        } else {
          setErrors((prev) => ({
            ...prev,
            confirmPassword: "",
          }));
        }
      }

      // Check if user is typing in confirm password field
      if (name === 'confirmPassword') {
        if (value && value !== newFormData.password) {
          setErrors((prev) => ({
            ...prev,
            confirmPassword: "Passwords do not match",
          }));
        } else if (value === newFormData.password) {
          setErrors((prev) => ({
            ...prev,
            confirmPassword: "",
          }));
        }
      }
    }
  };

  // Handle React Select changes
  const handleSelectChange = (selectedOption, actionMeta) => {
    const { name } = actionMeta;
    const value = selectedOption ? selectedOption.value : "";

    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // Clear error when user makes selection
    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  const validateForm = () => {
    // Use the validation function from the service
    const newErrors = validateUserData(formData, !!item);
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Call parent onSubmit callback with form data
      // The parent component will handle the API call
      if (onSubmit) {
        await onSubmit(formData);
      }

      // Close the form on success
      onClose();
    } catch (error) {
      console.error("Error submitting form:", error);
      // Error handling is done in the parent component with toast notifications
      // Form stays open so user can correct errors
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {/* Row 1: Name and Phone side by side */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Name */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Full Name *
          </label>
          <input
            type="text"
            name="name"
            value={formData.name}
            onChange={handleChange}
            className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white outline-none transition-colors duration-200 ${
              errors.name
                ? "border-red-500 dark:border-red-400"
                : "border-gray-300 dark:border-gray-600 focus:border-green-500 hover:border-gray-400"
            }`}
            placeholder="Enter full name"
          />
          {errors.name && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {errors.name}
            </p>
          )}
        </div>

        {/* Phone */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Phone Number *
          </label>
          <input
            type="tel"
            name="phone_number"
            value={formData.phone_number}
            onChange={handleChange}
            className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white outline-none transition-colors duration-200 ${
              errors.phone_number
                ? "border-red-500 dark:border-red-400"
                : "border-gray-300 dark:border-gray-600 focus:border-green-500 hover:border-gray-400"
            }`}
            placeholder="Enter phone number"
          />
          {errors.phone_number && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {errors.phone_number}
            </p>
          )}
        </div>
      </div>

      {/* Row 1.5: Email and RM Code side by side */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Email */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Email Address *
          </label>
          <input
            type="email"
            name="email"
            value={formData.email}
            onChange={handleChange}
            className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white outline-none transition-colors duration-200 ${
              errors.email
                ? "border-red-500 dark:border-red-400"
                : "border-gray-300 dark:border-gray-600 focus:border-green-500 hover:border-gray-400"
            }`}
            placeholder="Enter email address"
          />
          {errors.email && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {errors.email}
            </p>
          )}
        </div>

        {/* RM Code */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            RM Code
            <span className="text-xs text-gray-500 dark:text-gray-400 ml-1">
              (optional)
            </span>
          </label>
          <input
            type="text"
            name="rm_code"
            value={formData.rm_code}
            onChange={handleChange}
            className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white outline-none transition-colors duration-200 ${
              errors.rm_code
                ? "border-red-500 dark:border-red-400"
                : "border-gray-300 dark:border-gray-600 focus:border-green-500 hover:border-gray-400"
            }`}
            placeholder="Enter RM code (e.g., RM001)"
          />
          {errors.rm_code && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {errors.rm_code}
            </p>
          )}
        </div>
      </div>

      {/* Row 2: Role and Branch side by side */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Role */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Role *
          </label>
          <Select
            name="role"
            options={roleOptions}
            value={
              roleOptions.find((option) => option.value === formData.role) ||
              null
            }
            onChange={handleSelectChange}
            placeholder={loadingOptions ? "Loading roles..." : "Select a role"}
            isLoading={loadingOptions}
            isDisabled={loadingOptions}
            styles={selectStyles}
            error={errors.role}
            className={errors.role ? "react-select-error" : ""}
            classNamePrefix="react-select"
          />
          {errors.role && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {errors.role}
            </p>
          )}
        </div>

        {/* Branch */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Branch *
          </label>
          <Select
            name="branch"
            options={branchOptions}
            value={
              branchOptions.find(
                (option) => option.value === formData.branch
              ) || null
            }
            onChange={handleSelectChange}
            placeholder={loadingOptions ? "Loading branches..." : "Select a branch"}
            isLoading={loadingOptions}
            isDisabled={loadingOptions}
            styles={selectStyles}
            error={errors.branch}
            className={errors.branch ? "react-select-error" : ""}
            classNamePrefix="react-select"
          />
          {errors.branch && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {errors.branch}
            </p>
          )}
        </div>
      </div>

      {/* Row 3: Password */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Password {!item && "*"}
          {item && (
            <span className="text-xs text-gray-500 dark:text-gray-400 ml-1">
              (leave empty to keep current password)
            </span>
          )}
        </label>
        <div className="relative">
          <input
            type={showPassword ? "text" : "password"}
            name="password"
            value={formData.password}
            onChange={handleChange}
            className={`w-full px-3 py-2 pr-10 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white outline-none transition-colors duration-200 ${
              errors.password
                ? "border-red-500 dark:border-red-400"
                : "border-gray-300 dark:border-gray-600 focus:border-green-500 hover:border-gray-400"
            }`}
            placeholder={
              item ? "Enter new password (optional)" : "Enter password"
            }
          />
          <button
            type="button"
            onClick={() => setShowPassword(!showPassword)}
            className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            {showPassword ? (
              <EyeOff className="h-4 w-4" />
            ) : (
              <Eye className="h-4 w-4" />
            )}
          </button>
        </div>
        {errors.password && (
          <p className="mt-1 text-sm text-red-600 dark:text-red-400">
            {errors.password}
          </p>
        )}
      </div>

      {/* Row 4: Confirm Password (Frontend validation only - not sent to backend) */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Confirm Password {!item && "*"}
        </label>
        <div className="relative">
          <input
            type={showConfirmPassword ? "text" : "password"}
            name="confirmPassword"
            value={formData.confirmPassword}
            onChange={handleChange}
            className={`w-full px-3 py-2 pr-16 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white outline-none transition-colors duration-200 ${
              errors.confirmPassword
                ? "border-red-500 dark:border-red-400"
                : getPasswordMatchStatus() === 'match'
                ? "border-green-500 dark:border-green-400"
                : "border-gray-300 dark:border-gray-600 focus:border-green-500 hover:border-gray-400"
            }`}
            placeholder={item ? "Confirm new password" : "Confirm password"}
          />

          {/* Password match/mismatch indicator */}
          <div className="absolute inset-y-0 right-10 flex items-center">
            {getPasswordMatchStatus() === 'match' && (
              <Check className="h-4 w-4 text-green-500" />
            )}
            {getPasswordMatchStatus() === 'mismatch' && (
              <X className="h-4 w-4 text-red-500" />
            )}
          </div>

          {/* Show/hide password button */}
          <button
            type="button"
            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
            className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            {showConfirmPassword ? (
              <EyeOff className="h-4 w-4" />
            ) : (
              <Eye className="h-4 w-4" />
            )}
          </button>
        </div>

        {/* Real-time validation message */}
        {errors.confirmPassword && (
          <p className="mt-1 text-sm text-red-600 dark:text-red-400 flex items-center">
            <X className="h-3 w-3 mr-1" />
            {errors.confirmPassword}
          </p>
        )}

        {/* Success message when passwords match */}
        {getPasswordMatchStatus() === 'match' && !errors.confirmPassword && (
          <p className="mt-1 text-sm text-green-600 dark:text-green-400 flex items-center">
            <Check className="h-3 w-3 mr-1" />
            Passwords match
          </p>
        )}
      </div>

      {/* Form Actions */}
      <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
        <button
          type="button"
          onClick={onClose}
          className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors duration-200"
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={isSubmitting}
          className="px-4 py-2 text-sm font-medium text-white bg-[#165026] hover:bg-green-700 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isSubmitting ? "Saving..." : item ? "Update User" : "Create User"}
        </button>
      </div>
    </form>
  );
};

export default UserForm;
