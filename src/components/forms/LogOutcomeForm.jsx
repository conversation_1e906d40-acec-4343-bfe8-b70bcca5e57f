import { useState } from "react";

const LogOutcomeForm = ({ item, onClose, onSubmit }) => {
  const [formData, setFormData] = useState({
    outcomeType: "Positive",
    notes: "",
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Available outcome types
  const outcomeTypes = ["Positive", "Negative"];

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.notes.trim()) {
      newErrors.notes = "Notes are required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Prepare data for API integration
      const submitData = {
        followUpId: item?.id,
        clientName: item?.clientName,
        outcomeType: formData.outcomeType,
        notes: formData.notes,
        loggedAt: new Date().toISOString(),
        loggedBy: "Current User", // In a real app, this would come from auth context
        // Include additional follow-up details for reference
        assignedOfficer: item?.assignedOfficer,
        followUpType: item?.followUpType,
        followUpReason: item?.followUpReason,
        anchorName: item?.anchorName,
      };

      await onSubmit(submitData, item); // Pass both submitData and original item
      onClose();
    } catch (error) {
      console.error("Error submitting outcome:", error);
      // Here you would typically show an error notification to the user
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header with client name */}
      <div className="text-center pb-4 border-b border-gray-200 dark:border-gray-600">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
          Log Outcome for {item?.clientName}
        </h3>
        <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
          Follow-up: {item?.followUpReason}
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Outcome Type */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
            Outcome Type *
          </label>
          <div className="space-y-2">
            {outcomeTypes.map((type) => (
              <label key={type} className="flex items-center">
                <input
                  type="radio"
                  name="outcomeType"
                  value={type}
                  checked={formData.outcomeType === type}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600"
                />
                <span className="ml-3 text-sm text-gray-700 dark:text-gray-300">
                  {type}
                </span>
              </label>
            ))}
          </div>
        </div>

        {/* Notes */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Notes *
          </label>
          <textarea
            name="notes"
            value={formData.notes}
            onChange={handleInputChange}
            rows={4}
            className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 outline-none transition-colors duration-200 ${
              errors.notes
                ? "border-red-500 dark:border-red-400"
                : "border-gray-300 dark:border-gray-600 focus:border-green-500 hover:border-gray-400"
            }`}
            placeholder="Describe the outcome of this follow-up in detail..."
          />
          {errors.notes && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {errors.notes}
            </p>
          )}
          <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
            Include details about the conversation, client response, next steps,
            etc.
          </p>
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-3 pt-4">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-lg hover:bg-gray-200 focus:ring-2 focus:ring-gray-500 dark:bg-gray-600 dark:text-gray-300 dark:border-gray-500 dark:hover:bg-gray-700"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isSubmitting}
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? "Logging..." : "Log Outcome"}
          </button>
        </div>
      </form>
    </div>
  );
};

export default LogOutcomeForm;
