import { useState, useEffect } from "react";
import { useApi } from "../../contexts/ApiContext";

//NOTE: if an item is passed in, it's an edit form
const BranchForm = ({ item, onClose, onSubmit }) => {
  const { branchApi, regionApi } = useApi();
  const [formData, setFormData] = useState({
    name: "",
    regionId: "",
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [regions, setRegions] = useState([]);
  const [loadingRegions, setLoadingRegions] = useState(false);

  // Fetch regions for dropdown
  useEffect(() => {
    const fetchRegions = async () => {
      setLoadingRegions(true);
      try {
        const data = await regionApi.getAll();
        setRegions(Array.isArray(data) ? data : []);
      } catch (error) {
        console.error("Error fetching regions:", error);
        setRegions([]);
      } finally {
        setLoadingRegions(false);
      }
    };

    fetchRegions();
  }, [regionApi]);

  // Populate form with item data for editing
  useEffect(() => {
    if (item) {
      setFormData({
        name: item.name || "",
        regionId: item.regionId || "",
      });
    }
  }, [item]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = "Branch name is required";
    }

    if (!formData.regionId) {
      newErrors.regionId = "Region is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      let result;

      if (item) {
        // Update existing branch
        result = await branchApi.update(item.id, formData);
      } else {
        // Create new branch
        result = await branchApi.create(formData);
      }

      if (onSubmit) {
        onSubmit(result);
      }

      console.log(item ? "Updated branch:" : "Created branch:", result);
      onClose();
    } catch (error) {
      console.error("Error submitting form:", error);
      // Error handling is done in the API context interceptor
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-1  gap-4">
        {/* Name */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Branch*
          </label>
          <input
            type="text"
            name="name"
            value={formData.name}
            onChange={handleChange}
            className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 outline-none transition-colors duration-200 ${
              errors.name
                ? "border-red-500 dark:border-red-400"
                : "border-gray-300 dark:border-gray-600 focus:border-green-500 hover:border-gray-400"
            }`}
            placeholder="Enter branch"
          />
          {errors.name && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {errors.name}
            </p>
          )}
        </div>

        {/* Region */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Region *
          </label>
          <select
            name="regionId"
            value={formData.regionId}
            onChange={handleChange}
            disabled={loadingRegions}
            className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white outline-none transition-colors duration-200 ${
              errors.regionId
                ? "border-red-500 dark:border-red-400"
                : "border-gray-300 dark:border-gray-600 focus:border-green-500 hover:border-gray-400"
            }`}
          >
            <option value="">
              {loadingRegions ? "Loading regions..." : "Select a region"}
            </option>
            {regions.map((region) => (
              <option key={region.id} value={region.id}>
                {region.name}
              </option>
            ))}
          </select>
          {errors.regionId && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {errors.regionId}
            </p>
          )}
        </div>
      </div>

      {/* Form Actions */}
      <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
        <button
          type="button"
          onClick={onClose}
          className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors duration-200"
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={isSubmitting}
          className="px-4 py-2 text-sm font-medium text-white bg-[#165026] hover:bg-green-700 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isSubmitting
            ? "Saving..."
            : item
            ? "Update Branch"
            : "Create Branch"}
        </button>
      </div>
    </form>
  );
};

export default BranchForm;
