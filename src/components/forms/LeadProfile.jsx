import { useState, useEffect } from "react";
import { Minus } from "lucide-react";
import {
  ProfileHeader,
  TabNavigation,
  ProfileTab,
  HistoryTab,
} from "./profile";
import { leadsService } from "../../services/leadsService";

const LeadProfile = ({ item, onClose }) => {
  const [activeTab, setActiveTab] = useState("profile");
  const [leadDetails, setLeadDetails] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch detailed lead data when component mounts
  useEffect(() => {
    const fetchLeadDetails = async () => {
      if (!item?.id) {
        setError("No lead ID provided");
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        console.log("Fetching lead details for profile:", item.id);
        const details = await leadsService.getById(item.id);
        console.log("Lead details fetched for profile:", details);
        setLeadDetails(details);
        setError(null);
      } catch (error) {
        console.error("Error fetching lead details for profile:", error);
        setError("Failed to load lead details");
      } finally {
        setLoading(false);
      }
    };

    fetchLeadDetails();
  }, [item?.id]);

  if (!item) return null;

  if (loading) {
    return (
      <div className="flex items-center justify-center h-[80vh]">
        <div className="flex items-center space-x-2">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
          <span className="text-gray-500">Loading profile...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-[80vh]">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            Close
          </button>
        </div>
      </div>
    );
  }

  // Helper function to get status color
  const getStatusColor = (status) => {
    switch (status) {
      case "Cold":
        return "#1c5b41";
      case "Pending":
        return "#ffb800";
      case "Warm":
        return "#369dc9";
      case "Hot":
        return "#ff0000";
      default:
        return "#7e7e7e";
    }
  };

  // Helper function to format field values
  const formatValue = (value) => {
    if (!value || value === "")
      return <Minus size={16} className="text-gray-400" />;
    return value;
  };

  return (
    <div className="flex flex-col h-[80vh]">
      {/* Fixed Header */}
      <div className="flex-shrink-0">
        <ProfileHeader
          item={item}
          onClose={onClose}
          getStatusColor={getStatusColor}
        />
        <TabNavigation activeTab={activeTab} setActiveTab={setActiveTab} />
      </div>

      {/* Scrollable Body */}
      <div className="flex-1 overflow-y-auto overflow-x-hidden scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
        {/* Tab Content */}
        {activeTab === "profile" && (
          <ProfileTab
            item={item}
            leadDetails={leadDetails}
            onClose={onClose}
            formatValue={formatValue}
          />
        )}

        {activeTab === "history" && (
          <HistoryTab onClose={onClose} item={item} />
        )}
      </div>
    </div>
  );
};

export default LeadProfile;
