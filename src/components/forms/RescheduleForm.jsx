import { useState, useEffect } from "react";

const RescheduleForm = ({ item, onClose, onSubmit }) => {
  const [formData, setFormData] = useState({
    date: "",
    reason: "",
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Populate form with item data for rescheduling
  useEffect(() => {
    if (item) {
      setFormData({
        date: item.date ? new Date(item.date).toISOString().slice(0, 16) : "",
        reason: "",
      });
    }
  }, [item]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.date) {
      newErrors.date = "Date is required";
    } else {
      const selectedDate = new Date(formData.date);
      const now = new Date();
      if (selectedDate < now) {
        newErrors.date = "Date cannot be in the past";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Prepare data for API integration
      const submitData = {
        date: formData.date,
        reason: formData.reason || "", // Ensure reason is always a string
        // Include original item for reference
        originalFollowUpId: item.id,
        clientName: item.clientName,
        anchorName: item.anchorName,
        assignedOfficer: item.assignedOfficer,
        followUpType: item.followUpType,
        followUpReason: item.followUpReason,
      };

      await onSubmit(submitData, item);
      onClose();
    } catch (error) {
      console.error("Error submitting reschedule form:", error);
      // Here you would typically show an error notification to the user
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header with client name */}
      <div className="text-center pb-4 border-b border-gray-200 dark:border-gray-600">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
          Reschedule Follow-up for {item?.clientName}
        </h3>
        <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
          Current: {item?.followUpReason} -{" "}
          {item?.date ? new Date(item.date).toLocaleDateString() : "N/A"}
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* New Date */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Date *
          </label>
          <input
            type="datetime-local"
            name="date"
            value={formData.date}
            onChange={handleInputChange}
            className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white outline-none transition-colors duration-200 ${
              errors.date
                ? "border-red-500 dark:border-red-400"
                : "border-gray-300 dark:border-gray-600 focus:border-green-500 hover:border-gray-400"
            }`}
          />
          {errors.date && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {errors.date}
            </p>
          )}
        </div>

        {/* Reason (Optional) */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Reason (Optional)
          </label>
          <textarea
            name="reason"
            value={formData.reason}
            onChange={handleInputChange}
            rows={3}
            className="w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 outline-none transition-colors duration-200 border-gray-300 dark:border-gray-600 focus:border-green-500 hover:border-gray-400"
            placeholder="Enter reason for rescheduling (optional)"
          />
          <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
            Provide a reason for rescheduling this follow-up
          </p>
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-3 pt-4">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-lg hover:bg-gray-200 focus:ring-2 focus:ring-gray-500 dark:bg-gray-600 dark:text-gray-300 dark:border-gray-500 dark:hover:bg-gray-700"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isSubmitting}
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? "Rescheduling..." : "Reschedule Follow-up"}
          </button>
        </div>
      </form>
    </div>
  );
};

export default RescheduleForm;
