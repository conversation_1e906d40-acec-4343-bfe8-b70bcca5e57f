import Sidebar from "../Sidebar";
import MobileSidebar from "../MobileSidebar";
import Navbar from "../Navbar";

function PrivateLayout({ children }) {
  return (
    <div className="h-screen bg-gray-50 dark:bg-black flex overflow-hidden transition-colors duration-200">
      {/* Sidebar Components */}
      <Sidebar />
      <MobileSidebar />

      {/* Main content area */}
      <div className="flex-1 overflow-hidden flex flex-col h-full">
        {/* Navbar Component */}
        <Navbar />

        {/* Page content - Scrollable */}
        <main className="flex-1 overflow-y-auto p-3 md:p-6 bg-[rgba(54,201,95,0.04)] dark:bg-gray-900 transition-colors duration-200 scrollbar-thin">
          {children}

          {/* Footer */}
          <footer className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
            <div className="text-center text-sm text-gray-600 dark:text-gray-400">
              Copyright © Designed & Developed by{" "}
              <a
                href="https://minesoftwares.com/"
                className="text-[#1c5b41] font-medium"
              >
                Mine Softwares Ltd
              </a>{" "}
              2025
            </div>
          </footer>
        </main>
      </div>
    </div>
  );
}

export default PrivateLayout;
