function PublicLayout({ children }) {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Public navbar - simple header */}
      {/* <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <div className="bg-green-600 text-white px-2 py-1 rounded text-sm font-bold">
                KB
              </div>
              <span className="ml-2 text-gray-600 font-medium">TRACKER</span>
            </div>
          </div>
        </div>
      </header> */}

      {/* Main content */}
      <main>{children}</main>
    </div>
  );
}

export default PublicLayout;
