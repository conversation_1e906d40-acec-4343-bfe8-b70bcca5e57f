import React from 'react';

/*
  TaskListCard Component: displays a list of tasks (upcoming or overdue activities).
  Props:
    - title: title of the list
    - tasks: array of task objects { id, type, customer, date }
*/
const TaskListCard = ({ title, tasks }) => (
  <div className="p-4 rounded-lg shadow bg-white">
    <h2 className="text-lg font-medium text-gray-700 mb-2">{title}</h2>
    <ul className="divide-y divide-gray-200">
      {tasks.map(task => (
        <li key={task.id} className="py-2">
          <div className="text-sm text-gray-500">{task.type} - {task.customer}</div>
          <div className="text-sm font-semibold text-gray-800">{task.date}</div>
        </li>
      ))}
    </ul>
  </div>
);

export default TaskListCard;
