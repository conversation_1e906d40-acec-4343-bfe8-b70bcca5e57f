import React from 'react';
import Chart from 'react-apexcharts';

/*
  CallsChart Component: displays a bar chart comparing calls made vs target.
  Props:
    - callsMade: number of calls made
    - callsTarget: target number of calls
    - shouldAnimate: boolean to control chart animations
*/
const CallsChart = ({ callsMade, callsTarget, shouldAnimate = false }) => {
  // Chart configuration for Calls vs Target (bar chart)
  const chartConfig = {
    series: [
      { name: 'Made', data: [callsMade] },
      { name: 'Target', data: [callsTarget] },
    ],
    options: {
      chart: {
        type: 'bar',
        toolbar: { show: false },
        animations: {
          enabled: shouldAnimate,
          easing: 'easeinout',
          speed: 800,
          animateGradually: {
            enabled: true,
            delay: 150,
          },
          dynamicAnimation: {
            enabled: true,
            speed: 350,
          },
        },
        background: 'transparent',
      },
      plotOptions: {
        bar: {
          horizontal: false,
          columnWidth: '50%',
          endingShape: 'flat',
          borderRadius: 0,
        }
      },
      xaxis: { categories: ['Calls MTD'] },
      colors: ['#69AF57', '#1C5B41'],
      legend: { position: 'top' },
      yaxis: { title: { text: 'Number' } },
      dataLabels: {
        enabled: false,
      },
      stroke: {
        show: false,
        width: 0,
      },
    }
  };

  return (
    <div className="p-4 rounded-lg shadow bg-white">
      <h2 className="text-lg font-medium text-gray-700 mb-2">Calls Made vs Target</h2>
      <Chart 
        options={chartConfig.options} 
        series={chartConfig.series} 
        type="bar" 
        height={300} 
      />
    </div>
  );
};

export default CallsChart;
