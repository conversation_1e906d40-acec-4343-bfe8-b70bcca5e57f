import React from 'react';
import Chart from 'react-apexcharts';

/*
  VisitsChart Component: displays a bar chart comparing visits made vs target.
  Props:
    - visitsMade: number of visits made
    - visitsTarget: target number of visits
    - shouldAnimate: boolean to control chart animations
*/
const VisitsChart = ({ visitsMade, visitsTarget, shouldAnimate = false }) => {
  // Chart configuration for Visits vs Target (bar chart)
  const chartConfig = {
    series: [
      { name: 'Made', data: [visitsMade] },
      { name: 'Target', data: [visitsTarget] },
    ],
    options: {
      chart: {
        type: 'bar',
        toolbar: { show: false },
        animations: {
          enabled: shouldAnimate,
          easing: 'easeinout',
          speed: 800,
          animateGradually: {
            enabled: true,
            delay: 300, // Slightly delayed from calls chart
          },
          dynamicAnimation: {
            enabled: true,
            speed: 350,
          },
        },
        background: 'transparent',
      },
      plotOptions: {
        bar: {
          horizontal: false,
          columnWidth: '50%',
          endingShape: 'flat',
          borderRadius: 0,
        }
      },
      xaxis: { categories: ['Visits MTD'] },
      colors: ['#69AF57', '#1C5B41'],
      legend: { position: 'top' },
      yaxis: { title: { text: 'Number' } },
      dataLabels: {
        enabled: false,
      },
      stroke: {
        show: false,
        width: 0,
      },
    }
  };

  return (
    <div className="p-4 rounded-lg shadow bg-white">
      <h2 className="text-lg font-medium text-gray-700 mb-2">Visits Made vs Target</h2>
      <Chart 
        options={chartConfig.options} 
        series={chartConfig.series} 
        type="bar" 
        height={300} 
      />
    </div>
  );
};

export default VisitsChart;
