import React from 'react';
import Chart from 'react-apexcharts';

function CallsRadialChart() {
  // Sample data for general calls
  const totalCalls = 2247;
  const targetCalls = 2500;
  const achievementPercentage = ((totalCalls / targetCalls) * 100).toFixed(1);

  const chartOptions = {
    chart: {
      type: 'radialBar',
      height: 300,
      animations: {
        enabled: true,
        easing: 'easeinout',
        speed: 800,
        animateGradually: {
          enabled: true,
          delay: 150
        }
      }
    },
    plotOptions: {
      radialBar: {
        startAngle: -90,
        endAngle: 90,
        hollow: {
          size: '60%',
          background: 'transparent'
        },
        track: {
          background: '#E5E7EB',
          strokeWidth: '100%',
          margin: 5
        },
        dataLabels: {
          name: {
            show: true,
            fontSize: '14px',
            fontWeight: 600,
            color: '#6b7280',
            offsetY: -10
          },
          value: {
            show: true,
            fontSize: '24px',
            fontWeight: 700,
            color: '#1f2937',
            offsetY: 10,
            formatter: function (val) {
              return val + '%';
            }
          }
        }
      }
    },
    colors: ['#4F46E5'], // Indigo color
    labels: ['Achievement'],
    stroke: {
      lineCap: 'round'
    },
    tooltip: {
      enabled: true,
      y: {
        formatter: function(val) {
          return `${totalCalls} calls (${val}% of target)`;
        }
      }
    },
    responsive: [
      {
        breakpoint: 768,
        options: {
          chart: {
            height: 250
          },
          plotOptions: {
            radialBar: {
              hollow: {
                size: '55%'
              }
            }
          }
        }
      }
    ]
  };

  const series = [parseFloat(achievementPercentage)];

  return (
    <div className="bg-white rounded-xl shadow-lg p-6">
      <div className="mb-4">
        <h3 className="text-lg font-semibold text-gray-800 text-center mb-2">
          Calls Progress
        </h3>
        <p className="text-sm text-gray-600 text-center">
          Total vs Target
        </p>
      </div>
      
      <Chart
        options={chartOptions}
        series={series}
        type="radialBar"
        height={300}
      />
      
      {/* Summary stats */}
      <div className="mt-4 grid grid-cols-2 gap-4 pt-4 border-t border-gray-200">
        <div className="text-center">
          <div className="text-xl font-bold text-indigo-600">
            {totalCalls.toLocaleString()}
          </div>
          <div className="text-xs text-gray-600">Total Calls</div>
        </div>
        <div className="text-center">
          <div className="text-xl font-bold text-gray-500">
            {targetCalls.toLocaleString()}
          </div>
          <div className="text-xs text-gray-600">Target</div>
        </div>
      </div>
    </div>
  );
}

export default CallsRadialChart;
