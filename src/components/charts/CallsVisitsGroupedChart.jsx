import React from "react";
import Chart from "react-apexcharts";

function CallsVisitsGroupedChart() {
  // Test data
  const callsActual = 2247;
  const callsTarget = 2500;
  const visitsActual = 1156;
  const visitsTarget = 1250;

  const chartOptions = {
    chart: {
      type: "bar",
      height: 400,
      toolbar: {
        show: false,
      },
      animations: {
        enabled: true,
        easing: "easeinout",
        speed: 800,
        animateGradually: {
          enabled: true,
          delay: 150,
        },
        dynamicAnimation: {
          enabled: true,
          speed: 350,
        },
      },
      background: "transparent",
    },
    plotOptions: {
      bar: {
        horizontal: true,
        barHeight: "80%",
        grouped: true,
        endingShape: "flat",
        borderRadius: 0,
        dataLabels: {
          position: "center",
        },
      },
    },
    dataLabels: {
      enabled: true,
      formatter: function (val) {
        return val.toLocaleString();
      },
      offsetX: 10,
      style: {
        fontSize: "15px",
        fontWeight: 600,
        colors: ["#ffffff"],
      },
    },
    stroke: {
      show: false,
      width: 0,
    },
    xaxis: {
      categories: ["Calls", "Visits"],
      labels: {
        style: {
          fontSize: "14px",
          fontWeight: 600,
          colors: ["#374151"],
        },
      },
      axisBorder: {
        show: true,
        color: "#e5e7eb",
      },
      axisTicks: {
        show: true,
        color: "#e5e7eb",
      },
      title: {
        text: "Number of Activities",
        style: {
          fontSize: "14px",
          fontWeight: 600,
          color: "#374151",
        },
      },
    },
    yaxis: {
      labels: {
        style: {
          fontSize: "14px",
          fontWeight: 600,
          colors: ["#374151"],
        },
      },
      title: {
        // text: "Activity Type",
        style: {
          fontSize: "14px",
          fontWeight: 600,
          color: "#374151",
        },
        offsetX: -10,
      },
    },
    fill: {
      opacity: 1,
      type: "solid",
    },
    colors: ["#82C355", "#1c5b41"], // Light green for actual, Dark green for target
    legend: {
      show: true,
      position: "top",
      horizontalAlign: "center",
      fontSize: "14px",
      fontWeight: 600,
      fontFamily: "Inter, system-ui, sans-serif",
      markers: {
        width: 14,
        height: 14,
        radius: 2,
        offsetX: -2,
      },
      itemMargin: {
        horizontal: 20,
        vertical: 0,
      },
      labels: {
        colors: ["#374151"],
      },
    },
    tooltip: {
      enabled: true,
      shared: false,
      intersect: true,
      y: {
        formatter: function (val, { seriesIndex, dataPointIndex }) {
          let percentage = "";
          if (seriesIndex === 0) {
            // Actual values
            if (dataPointIndex === 0) {
              // Calls
              percentage = ` (${((val / callsTarget) * 100).toFixed(
                1
              )}% of target)`;
            } else {
              // Visits
              percentage = ` (${((val / visitsTarget) * 100).toFixed(
                1
              )}% of target)`;
            }
          }

          return `${val.toLocaleString()}${percentage}`;
        },
      },
      style: {
        fontSize: "12px",
      },
    },
    grid: {
      borderColor: "#e5e7eb",
      strokeDashArray: 0,
      xaxis: {
        lines: {
          show: true,
        },
      },
      yaxis: {
        lines: {
          show: false,
        },
      },
      padding: {
        top: 0,
        right: 20,
        bottom: 0,
        left: 10,
      },
    },
    responsive: [
      {
        breakpoint: 768,
        options: {
          chart: {
            height: 250,
          },
          plotOptions: {
            bar: {
              barHeight: "50%",
            },
          },
          dataLabels: {
            style: {
              fontSize: "13px",
              colors: ["#ffffff"],
            },
          },
          legend: {
            fontSize: "12px",
            itemMargin: {
              horizontal: 10,
              vertical: 3,
            },
          },
        },
      },
    ],
  };

  const series = [
    {
      name: "Actual",
      data: [callsActual, visitsActual],
    },
    {
      name: "Target",
      data: [callsTarget, visitsTarget],
    },
  ];

  return (
    <div className="bg-white rounded-xl shadow-lg p-6 ">
      <div className="mb-6">
        <h3 className="text-[23px] font-semibold text-gray-800 text-center mb-2">
          Monthly Calls & Visits Performance
        </h3>
        <p className="text-sm text-gray-600 text-center">
          Actual vs Target Comparison
        </p>
      </div>

      <Chart options={chartOptions} series={series} type="bar" height={350} />

      {/* Summary stats */}
      <div className="mt-6 grid grid-cols-2 md:grid-cols-4 gap-4 pt-4 border-t border-gray-200">
        <div className="text-center">
          <div className="text-lg font-bold" style={{ color: "#82C355" }}>
            {callsActual.toLocaleString()}
          </div>
          <div className="text-xs text-gray-600">Calls Made</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-bold" style={{ color: "#1c5b41" }}>
            {callsTarget.toLocaleString()}
          </div>
          <div className="text-xs text-gray-600">Calls Target</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-bold" style={{ color: "#82C355" }}>
            {visitsActual.toLocaleString()}
          </div>
          <div className="text-xs text-gray-600">Visits Made</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-bold" style={{ color: "#1c5b41" }}>
            {visitsTarget.toLocaleString()}
          </div>
          <div className="text-xs text-gray-600">Visits Target</div>
        </div>
      </div>
    </div>
  );
}

export default CallsVisitsGroupedChart;
