:root {
  --primary: #333333;
  --secondary: #A336C9;
  --primary-hover: #2ba14c;
  --primary-light: #afe9bf;
  --primary-dark: #165026;
  --rgba-primary-1: rgba(54, 201, 95, 0.1);
  --rgba-primary-2: rgba(54, 201, 95, 0.2);
  --rgba-primary-3: rgba(54, 201, 95, 0.3);
  --rgba-primary-4: rgba(54, 201, 95, 0.4);
  --rgba-primary-5: rgba(54, 201, 95, 0.5);
  --rgba-primary-6: rgba(54, 201, 95, 0.6);
  --rgba-primary-7: rgba(54, 201, 95, 0.7);
  --rgba-primary-8: rgba(54, 201, 95, 0.8);
  --rgba-primary-9: rgba(54, 201, 95, 0.9);
}

body {
  font-family: 'Poppins', sans-serif;
}


.hamburger {
  display: inline-block;
  left: 0px;
  position: relative;
  top: 3px;
  -webkit-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  width: 26px;
  z-index: 10;
}

.hamburger .line {
  background: var(--primary) !important;
  display: block;
  height: 3px;
  border-radius: 3px;
  margin-top: 6px;
  margin-bottom: 6px;
  margin-left: auto;
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}

/* Dark mode hamburger */
.dark .hamburger .line {
  background: #ffffff;
}

.hamburger .line:nth-child(1) {
  width: 20px;
}

.hamburger .line:nth-child(2) {
  width: 26px;
}

.hamburger .line:nth-child(3) {
  width: 22px;
}

.hamburger:hover {
  cursor: pointer;
}

.hamburger:hover .line {
  width: 26px;
}

.hamburger.is-active .line:nth-child(1),
.hamburger.is-active .line:nth-child(3) {
  width: 10px;
  height: 2px;
}

.hamburger.is-active .line:nth-child(2) {
  -webkit-transform: translateX(0px);
  transform: translateX(0px);
  width: 22px;
  height: 2px;
}

.hamburger.is-active .line:nth-child(1) {
  -webkit-transform: translateY(4px) rotate(45deg);
  transform: translateY(4px) rotate(45deg);
}

.hamburger.is-active .line:nth-child(3) {
  -webkit-transform: translateY(-4px) rotate(-45deg);
  transform: translateY(-4px) rotate(-45deg);
}

button {
  cursor: pointer;
}