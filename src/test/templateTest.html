<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Template Test</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
</head>
<body>
    <h1>Excel Template Test</h1>
    <button onclick="generateTemplate()">Generate Template</button>
    <div id="output"></div>

    <script>
        // Lead template fields
        const LEAD_TEMPLATE_FIELDS = [
            { key: 'customerName', label: 'Customer Full Name *', required: true },
            { key: 'phoneNumber', label: 'Phone Number * (07xxxxxxxx)', required: true },
            { key: 'customerCategory', label: 'Customer Category (Employed/Self Employed/etc)', required: false },
            { key: 'isicSector', label: 'Industry Sector (ISIC)', required: false },
            { key: 'leadType', label: 'Lead Type (New/Existing)', required: false },
            { key: 'clientId', label: 'Client ID (Required for Existing)', required: false },
            { key: 'branchName', label: 'Branch Name', required: false },
            { key: 'contactPersonName', label: 'Contact Person Name', required: false },
            { key: 'contactPersonPhone', label: 'Contact Person Phone', required: false },
            { key: 'employerName', label: 'Employer/Company Name', required: false },
        ];

        // Sample data
        const SAMPLE_LEAD_DATA = [
            {
                customerName: 'John Kamau Doe',
                phoneNumber: '0712345678',
                customerCategory: 'Employed',
                isicSector: 'Manufacturing',
                leadType: 'New',
                clientId: '',
                branchName: 'Nairobi Main Branch',
                contactPersonName: 'Jane Wanjiku',
                contactPersonPhone: '0712345679',
                employerName: 'ABC Manufacturing Ltd',
            },
            {
                customerName: 'Mary Wanjiku Smith',
                phoneNumber: '0723456789',
                customerCategory: 'Self Employed',
                isicSector: 'Retail Trade',
                leadType: 'Existing',
                clientId: 'CL001234',
                branchName: 'Westlands Branch',
                contactPersonName: '',
                contactPersonPhone: '',
                employerName: 'Mary\'s Boutique',
            }
        ];

        function generateTemplate() {
            try {
                // Create a new workbook
                const workbook = XLSX.utils.book_new();

                // Create template sheet with headers and sample data
                const headers = LEAD_TEMPLATE_FIELDS.map(field => field.label);
                
                // Add instruction row at the top
                const instructionRow = [
                    'LEADS IMPORT TEMPLATE - Fill in your lead data below. Required fields marked with (*). Delete sample data before importing.',
                    '', '', '', '', '', '', '', '', ''
                ];
                
                // Add notes row
                const notesRow = [
                    'NOTES: Phone format 07xxxxxxxx or 01xxxxxxxx | Lead Type: New or Existing | Client ID required for Existing leads | Max 1000 leads per import',
                    '', '', '', '', '', '', '', '', ''
                ];
                
                // Create template data with instruction rows, headers, and sample data
                const templateData = [
                    instructionRow,
                    notesRow,
                    [], // Empty row for spacing
                    headers, 
                    ...SAMPLE_LEAD_DATA.map(row => 
                        LEAD_TEMPLATE_FIELDS.map(field => row[field.key] || '')
                    )
                ];

                const templateSheet = XLSX.utils.aoa_to_sheet(templateData);
                
                // Set column widths
                const columnWidths = LEAD_TEMPLATE_FIELDS.map(() => ({ wch: 20 }));
                templateSheet['!cols'] = columnWidths;

                XLSX.utils.book_append_sheet(workbook, templateSheet, 'Leads Import Template');

                // Generate and download the file
                const fileName = `Leads-Import-Template-${new Date().toISOString().split('T')[0]}.xlsx`;
                XLSX.writeFile(workbook, fileName);

                document.getElementById('output').innerHTML = '<p style="color: green;">✅ Template generated successfully!</p>';
            } catch (error) {
                console.error('Error generating template:', error);
                document.getElementById('output').innerHTML = '<p style="color: red;">❌ Error: ' + error.message + '</p>';
            }
        }
    </script>
</body>
</html>
