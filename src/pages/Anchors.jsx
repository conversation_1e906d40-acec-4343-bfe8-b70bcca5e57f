import { useState } from "react";
import PrivateLayout from "../components/layouts/PrivateLayout";
import DataTable from "../components/common/DataTable";
import AnchorForm from "../components/forms/AnchorForm";
import DeleteConfirmation from "../components/forms/DeleteConfirmation";

const Anchors = () => {
  const [loading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);

  // Define columns of the table here and what to render
  const columns = [
    {
      key: "anchorID",
      title: "ANCHOR ID",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400 font-mono">
          {value}
        </span>
      ),
    },
    {
      key: "name",
      title: "NAME",
      render: (value) => (
        <span className="font-medium text-gray-900 dark:text-white">
          {value}
        </span>
      ),
    },
    {
      key: "type",
      title: "TYPE",
      render: (value) => (
        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
          {value}
        </span>
      ),
    },
    {
      key: "location",
      title: "LOCATION",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value}
        </span>
      ),
    },
    {
      key: "createdBy",
      title: "CREATED BY",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value}
        </span>
      ),
    },
    {
      key: "createdAt",
      title: "CREATED AT",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {new Date(value).toLocaleDateString()}
        </span>
      ),
    },
    {
      key: "numberOfCustomers",
      title: "NUMBER OF CUSTOMERS",
      render: (value) => (
        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
          {value}
        </span>
      ),
    },
  ];

  // Data to be rendered in the table, should match the columns above
  const sampleAnchors = [
    {
      id: "anchor001",
      anchorID: "ANC001",
      name: "Joy Mini Mart",
      type: "Retail Store",
      location: "Nairobi, Kenya",
      createdBy: "John Doe",
      createdAt: "2024-01-15T10:30:00Z",
      numberOfCustomers: 245,
    },
    {
      id: "anchor002",
      anchorID: "ANC002",
      name: "Aluworks Enterprises",
      type: "Manufacturing",
      location: "Nairobi, Kenya",
      createdBy: "Jane Smith",
      createdAt: "2024-01-20T14:45:00Z",
      numberOfCustomers: 189,
    },
    {
      id: "anchor003",
      anchorID: "ANC003",
      name: "Kikuyu Pipes",
      type: "Distribution",
      location: "Kikuyu, Kenya",
      createdBy: "Michael Johnson",
      createdAt: "2024-02-01T09:15:00Z",
      numberOfCustomers: 156,
    },
    {
      id: "anchor004",
      anchorID: "ANC004",
      name: "Twiga Foods",
      type: "Food Service",
      location: "Nairobi, Kenya",
      createdBy: "Sarah Wilson",
      createdAt: "2024-02-05T11:20:00Z",
      numberOfCustomers: 312,
    },
    {
      id: "anchor005",
      anchorID: "ANC005",
      name: "Green Valley Farms",
      type: "Agriculture",
      location: "Nakuru, Kenya",
      createdBy: "David Brown",
      createdAt: "2024-02-10T16:30:00Z",
      numberOfCustomers: 98,
    },
  ];

  // Form submission handlers
  const handleCreateSubmit = async (formData) => {
    console.log("Creating anchor:", formData);

    try {
      // Prepare data for API integration
      const apiData = {
        ...formData,
        createdAt: new Date().toISOString(),
        createdBy: "Current User", // In a real app, this would come from auth context
        numberOfCustomers: 0, // Default value for new anchors
        // Generate anchor ID (in real app, this would be done by backend)
        anchorID: `ANC${String(Date.now()).slice(-3).padStart(3, "0")}`,
      };

      console.log("API data prepared for creation:", apiData);

      // API Integration: POST /api/anchors
      // Uncomment when API is ready
      // const response = await fetch('/api/anchors', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(apiData)
      // });

      // if (!response.ok) {
      //   throw new Error('Failed to create anchor');
      // }

      // const result = await response.json();
      // console.log("Anchor created successfully:", result);

      // For now, simulate success
      setTimeout(() => {
        console.log("Simulated API success for anchor creation");
        // Here you would typically update the local data or refetch
      }, 500);
    } catch (error) {
      console.error("Error creating anchor:", error);
      // Here you would typically show an error notification
    }
  };

  const handleEditSubmit = async (formData, originalItem) => {
    console.log("Updating anchor:", { originalItem, newData: formData });

    try {
      // Prepare data for API integration
      const apiData = {
        ...formData,
        updatedAt: new Date().toISOString(),
        updatedBy: "Current User", // In a real app, this would come from auth context
        // Preserve original creation data
        createdAt: originalItem.createdAt,
        createdBy: originalItem.createdBy,
        numberOfCustomers: originalItem.numberOfCustomers,
      };

      console.log("API data prepared for update:", apiData);

      // API Integration: PUT /api/anchors/{id}
      // Uncomment when API is ready
      // const response = await fetch(`/api/anchors/${originalItem.id}`, {
      //   method: 'PUT',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(apiData)
      // });

      // if (!response.ok) {
      //   throw new Error('Failed to update anchor');
      // }

      // const result = await response.json();
      // console.log("Anchor updated successfully:", result);

      // For now, simulate success
      setTimeout(() => {
        console.log("Simulated API success for anchor update");
        // Here you would typically update the local data or refetch
      }, 500);
    } catch (error) {
      console.error("Error updating anchor:", error);
      // Here you would typically show an error notification
    }
  };

  const handleDeleteConfirm = async (anchor) => {
    console.log("Deleting anchor:", anchor);

    try {
      // API Integration: DELETE /api/anchors/{id}
      // Uncomment when API is ready
      // const response = await fetch(`/api/anchors/${anchor.id}`, {
      //   method: 'DELETE',
      //   headers: { 'Content-Type': 'application/json' }
      // });

      // if (!response.ok) {
      //   throw new Error('Failed to delete anchor');
      // }

      // console.log("Anchor deleted successfully");

      // For now, simulate success
      setTimeout(() => {
        console.log("Simulated API success for anchor deletion");
        // Here you would typically update the local data or refetch
      }, 500);
    } catch (error) {
      console.error("Error deleting anchor:", error);
      // Here you would typically show an error notification
    }
  };

  // Import/Export handlers
  const handleExport = () => {
    console.log("Exporting anchors data...");
    // Here you would typically generate and download the export file
    // For now, we'll just log the action
  };

  const handleImport = (file) => {
    console.log("Importing anchors from file:", file.name);
    // Here you would typically process the uploaded file
    // Parse the data and make API calls to import anchors
  };

  const handleView = (anchor) => {
    console.log("View anchor:", anchor);
    // Here you would typically navigate to view page or show view modal
  };

  const handleLoadMore = () => {
    setLoadingMore(true);
    // Simulate API call
    setTimeout(() => {
      setLoadingMore(false);
      console.log("Load more anchors");
    }, 2000);
  };

  return (
    <PrivateLayout>
      <div className="">
        {/* Data Table */}
        <DataTable
          columns={columns}
          data={sampleAnchors}
          searchPlaceholder="Search ..."
          addButtonText="New Anchor"
          onView={handleView}
          actions={["view", "edit", "delete"]}
          loading={loading}
          loadingMore={loadingMore}
          loadMoreText="Load More Anchors"
          onLoadMore={handleLoadMore}
          showLoadMore={true}
          highlightField="type"
          highlightColors={{
            "Retail Store":
              "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",
            Manufacturing:
              "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400",
            Distribution:
              "bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400",
            "Food Service":
              "bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400",
            Agriculture:
              "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400",
          }}
          // Modal forms
          createForm={({ onClose }) => (
            <AnchorForm onClose={onClose} onSubmit={handleCreateSubmit} />
          )}
          editForm={({ item, onClose }) => (
            <AnchorForm
              item={item}
              onClose={onClose}
              onSubmit={handleEditSubmit}
            />
          )}
          deleteForm={({ item, onClose }) => (
            <DeleteConfirmation
              item={item}
              onClose={onClose}
              onConfirm={handleDeleteConfirm}
              itemName="Anchor"
            />
          )}
          createModalTitle="Create New Anchor"
          editModalTitle="Edit Anchor"
          deleteModalTitle=""
          modalSize="lg"
          deleteModalSize="sm"
          // Import/Export functionality
          showImportExport={true}
          onExport={handleExport}
          onImport={handleImport}
          importModalTitle="Import Anchors"
          importTemplateFileName="Anchors-Template.xlsx"
          importAcceptedFileTypes=".xlsx,.xls,.csv"
        />
      </div>
    </PrivateLayout>
  );
};

export default Anchors;
