import { useState, useEffect, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import PrivateLayout from "../components/layouts/PrivateLayout";
import DataTable from "../components/common/DataTable";
import DeleteConfirmation from "../components/forms/DeleteConfirmation";
import { rolesService } from "../services/permissionsService";
import { toast } from "react-toastify";

const Roles = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [roles, setRoles] = useState([]);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Define columns of the table here and what to render
  const columns = [
    {
      key: "name",
      title: "ROLE NAME",
      render: (value) => (
        <span className="font-medium text-gray-900 dark:text-white">
          {value}
        </span>
      ),
    },
    {
      key: "description",
      title: "DESCRIPTION",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value}
        </span>
      ),
    },
    {
      key: "userCount",
      title: "USERS",
      render: (value) => (
        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
          {value} users
        </span>
      ),
    },
    {
      key: "permissionCount",
      title: "PERMISSIONS",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value || 0} permissions
        </span>
      ),
    },
  ];

  // Fetch roles data
  const fetchRoles = useCallback(async () => {
    setLoading(true);
    try {
      const data = await rolesService.getAll();
      // Ensure data is always an array and map to expected format
      const rolesArray = Array.isArray(data) ? data : (data?.data || []);
      const mappedRoles = rolesArray.map(role => ({
        id: role.id,
        name: role.name,
        description: role.description || "No description",
        userCount: role.userCount || 0,
        permissionCount: role.permissionCount || 0,
        // Add status field (you can modify this based on your backend logic)
        status: "Active" // Default to Active, modify based on your backend
      }));
      setRoles(mappedRoles);
    } catch (error) {
      console.error("Error fetching roles:", error);
      setRoles([]);
      toast.error("Failed to load roles");
    } finally {
      setLoading(false);
    }
  }, []);

  // Load roles on component mount and when refresh is triggered
  useEffect(() => {
    fetchRoles();
  }, [fetchRoles, refreshTrigger]);

  // Helper function to trigger refresh
  const refreshData = () => {
    setRefreshTrigger(prev => prev + 1);
  };



  // Navigation handlers
  const handleCreate = () => {
    navigate("/admin/roles/create");
  };

  const handleEdit = (role) => {
    navigate(`/admin/roles/edit/${role.id}`);
  };

  const handleDeleteConfirm = async (role) => {
    try {
      await rolesService.delete(role.id);
      console.log("Deleted role:", role);
      // Refresh the data after successful deletion
      refreshData();
    } catch (error) {
      console.error("Error deleting role:", error);
      // Error is handled by the service with toast notifications
    }
  };

  const handleView = (role) => {
    console.log("View role:", role);
    // Here you would typically navigate to view page or show view modal
  };

  const handleLoadMore = () => {
    setLoadingMore(true);
    // Simulate API call
    setTimeout(() => {
      setLoadingMore(false);
      console.log("Load more roles");
    }, 2000);
  };

  return (
    <PrivateLayout>
      <div className="">
        {/* Data Table */}
        <DataTable
          columns={columns}
          data={roles}
          searchPlaceholder="Search roles..."
          addButtonText="New Role"
          onView={handleView}
          onAdd={handleCreate}
          onEdit={handleEdit}
          actions={["view", "edit", "delete"]}
          loading={loading}
          loadingMore={loadingMore}
          loadMoreText="Load More Roles"
          onLoadMore={handleLoadMore}
          showLoadMore={true}
          highlightField="status"
          highlightColors={{
            Active:
              "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",
            Inactive:
              "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400",
          }}
          // Only delete modal needed
          deleteForm={({ item, onClose }) => (
            <DeleteConfirmation
              item={item}
              onClose={onClose}
              onConfirm={handleDeleteConfirm}
              itemName="Role"
            />
          )}
          deleteModalTitle=""
          deleteModalSize="sm"
        />
      </div>
    </PrivateLayout>
  );
};

export default Roles;
