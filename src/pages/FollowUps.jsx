import { useState } from "react";
import PrivateLayout from "../components/layouts/PrivateLayout";
import DataTable from "../components/common/DataTable";
import FollowUpForm from "../components/forms/FollowUpForm";
import RescheduleForm from "../components/forms/RescheduleForm";
import LogOutcomeForm from "../components/forms/LogOutcomeForm";
import DeleteConfirmation from "../components/forms/DeleteConfirmation";

const FollowUps = () => {
  const [loading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);

  // Define columns of the table here and what to render
  const columns = [
    {
      key: "clientName",
      title: "CLIENT NAME",
      render: (value) => (
        <span className="font-medium text-gray-900 dark:text-white">
          {value}
        </span>
      ),
    },
    {
      key: "anchorName",
      title: "ANCHOR NAME",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value || "N/A"}
        </span>
      ),
    },
    {
      key: "assignedOfficer",
      title: "ASSIGNED OFFICER",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value}
        </span>
      ),
    },
    {
      key: "followUpType",
      title: "FOLLOW-UP TYPE",
      render: (value) => {
        const getTypeColor = (type) => {
          switch (type) {
            case "Phone Call":
              return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400";
            case "Site Visit":
              return "bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400";
            default:
              return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
          }
        };

        return (
          <span
            className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getTypeColor(
              value
            )}`}
          >
            {value}
          </span>
        );
      },
    },
    {
      key: "followUpReason",
      title: "FOLLOW-UP REASON",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value}
        </span>
      ),
    },
    {
      key: "date",
      title: "DATE",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {new Date(value).toLocaleDateString()}
        </span>
      ),
    },
    {
      key: "status",
      title: "STATUS",
      render: (value) => {
        const getStatusColor = (status) => {
          switch (status) {
            case "Pending":
              return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400";
            case "Completed":
              return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";
            case "Canceled":
              return "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400";
            default:
              return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
          }
        };

        return (
          <span
            className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(
              value
            )}`}
          >
            {value}
          </span>
        );
      },
    },
    {
      key: "createdDate",
      title: "CREATED DATE",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {new Date(value).toLocaleDateString()}
        </span>
      ),
    },
  ];

  // Sample follow-ups data
  const sampleFollowUps = [
    {
      id: "followup001",
      clientName: "John Kamau",
      anchorName: "Joy Mini Mart",
      assignedOfficer: "Sarah Wilson",
      followUpType: "Phone Call",
      followUpReason: "Loan Application Follow-up",
      date: "2024-02-20T10:00:00Z",
      status: "Pending",
      createdDate: "2024-02-15T09:30:00Z",
    },
    {
      id: "followup002",
      clientName: "Mary Wanjiku",
      anchorName: null,
      assignedOfficer: "Michael Johnson",
      followUpType: "Site Visit",
      followUpReason: "Business Assessment",
      date: "2024-02-21T14:00:00Z",
      status: "Completed",
      createdDate: "2024-02-16T11:15:00Z",
    },
    {
      id: "followup003",
      clientName: "Peter Mwangi",
      anchorName: "Kikuyu Pipes",
      assignedOfficer: "David Brown",
      followUpType: "Site Visit",
      followUpReason: "Document Collection",
      date: "2024-02-22T09:00:00Z",
      status: "Pending",
      createdDate: "2024-02-17T16:45:00Z",
    },
    {
      id: "followup004",
      clientName: "Grace Akinyi",
      anchorName: "Twiga Foods",
      assignedOfficer: "Emily Davis",
      followUpType: "Phone Call",
      followUpReason: "Payment Reminder",
      date: "2024-02-19T15:30:00Z",
      status: "Canceled",
      createdDate: "2024-02-14T13:20:00Z",
    },
    {
      id: "followup005",
      clientName: "James Ochieng",
      anchorName: "Green Valley Farms",
      assignedOfficer: "Robert Miller",
      followUpType: "Site Visit",
      followUpReason: "Contract Discussion",
      date: "2024-02-23T11:00:00Z",
      status: "Pending",
      createdDate: "2024-02-18T08:30:00Z",
    },
  ];

  // Form submission handlers
  const handleCreateSubmit = async (formData) => {
    console.log("Creating follow-up:", formData);

    try {
      // Prepare data for API integration
      const apiData = {
        ...formData,
        createdAt: new Date().toISOString(),
        status: "Pending", // Default status for new follow-ups
        // Add any additional fields that might be required by the API
      };

      console.log("API data prepared for creation:", apiData);

      // API Integration: POST /api/follow-ups
      // Uncomment when API is ready
      // const response = await fetch('/api/follow-ups', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(apiData)
      // });

      // if (!response.ok) {
      //   throw new Error('Failed to create follow-up');
      // }

      // const result = await response.json();
      // console.log("Follow-up created successfully:", result);

      // For now, simulate success
      setTimeout(() => {
        console.log("Simulated API success for creation");
        // Here you would typically update the local data or refetch
      }, 500);
    } catch (error) {
      console.error("Error creating follow-up:", error);
      // Here you would typically show an error notification
    }
  };

  const handleEditSubmit = (formData) => {
    console.log("Updating follow-up:", formData);
    // API Integration: PUT /api/follow-ups/{id}
    // const response = await fetch(`/api/follow-ups/${formData.id}`, {
    //   method: 'PUT',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify(formData)
    // });
  };

  const handleRescheduleSubmit = async (formData, originalItem) => {
    console.log("Rescheduling follow-up:", { originalItem, newData: formData });

    try {
      // Prepare data for API integration
      const apiData = {
        originalFollowUpId: originalItem.id,
        newDate: formData.date,
        reason: formData.reason || "No reason provided",
        // Include all original follow-up details for reference
        clientName: originalItem.clientName,
        anchorName: originalItem.anchorName,
        assignedOfficer: originalItem.assignedOfficer,
        followUpType: originalItem.followUpType,
        followUpReason: originalItem.followUpReason,
        status: "Pending", // Reset status to pending for rescheduled follow-up
      };

      console.log("API data prepared for reschedule:", apiData);

      // API Integration: PUT /api/follow-ups/{id}/reschedule
      // Uncomment when API is ready
      // const response = await fetch(`/api/follow-ups/${originalItem.id}/reschedule`, {
      //   method: 'PUT',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(apiData)
      // });

      // if (!response.ok) {
      //   throw new Error('Failed to reschedule follow-up');
      // }

      // const result = await response.json();
      // console.log("Reschedule successful:", result);

      // For now, simulate success
      setTimeout(() => {
        console.log("Simulated API success");
        // Here you would typically update the local data or refetch
      }, 500);
    } catch (error) {
      console.error("Error rescheduling follow-up:", error);
      // Here you would typically show an error notification
    } finally {
      setSelectedItem(null); // Clear selected item after submission
    }
  };

  const handleLogOutcomeSubmit = async (formData, originalItem) => {
    console.log("Logging outcome for follow-up:", {
      originalItem,
      outcome: formData,
    });

    try {
      // Prepare data for API integration
      const apiData = {
        followUpId: originalItem.id,
        clientName: originalItem.clientName,
        outcomeType: formData.outcomeType,
        notes: formData.notes,
        loggedAt: new Date().toISOString(),
        // Include additional details that might be needed by the API
        assignedOfficer: originalItem.assignedOfficer,
        followUpType: originalItem.followUpType,
        followUpReason: originalItem.followUpReason,
        // Update status based on outcome
        status: "Completed",
      };

      console.log("API data prepared for outcome:", apiData);

      // API Integration: POST /api/follow-ups/{id}/outcome
      // Uncomment when API is ready
      // const response = await fetch(`/api/follow-ups/${originalItem.id}/outcome`, {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(apiData)
      // });

      // if (!response.ok) {
      //   throw new Error('Failed to log outcome');
      // }

      // const result = await response.json();
      // console.log("Outcome logged successfully:", result);

      // For now, simulate success
      setTimeout(() => {
        console.log("Simulated API success for outcome logging");
        // Here you would typically update the local data or refetch
      }, 500);
    } catch (error) {
      console.error("Error logging outcome:", error);
      // Here you would typically show an error notification
    } finally {
      setSelectedItem(null); // Clear selected item after logging outcome
    }
  };

  const handleCancelConfirm = async (followUp) => {
    console.log("Canceling follow-up:", followUp);

    try {
      // Prepare data for API integration
      const apiData = {
        followUpId: followUp.id,
        clientName: followUp.clientName,
        reason: "Canceled by user",
        canceledAt: new Date().toISOString(),
        // Include additional details that might be needed by the API
        assignedOfficer: followUp.assignedOfficer,
        followUpType: followUp.followUpType,
        followUpReason: followUp.followUpReason,
        // Update status to canceled
        status: "Canceled",
      };

      console.log("API data prepared for cancellation:", apiData);

      // API Integration: PUT /api/follow-ups/{id}/cancel
      // Uncomment when API is ready
      // const response = await fetch(`/api/follow-ups/${followUp.id}/cancel`, {
      //   method: 'PUT',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(apiData)
      // });

      // if (!response.ok) {
      //   throw new Error('Failed to cancel follow-up');
      // }

      // const result = await response.json();
      // console.log("Follow-up canceled successfully:", result);

      // For now, simulate success
      setTimeout(() => {
        console.log("Simulated API success for cancellation");
        // Here you would typically update the local data or refetch
      }, 500);
    } catch (error) {
      console.error("Error canceling follow-up:", error);
      // Here you would typically show an error notification
    } finally {
      setSelectedItem(null); // Clear selected item after cancellation
    }
  };

  const handleView = async (followUp) => {
    setSelectedItem(followUp); // Set selected item for view
    console.log("View follow-up:", followUp);

    try {
      // API Integration: GET /api/follow-ups/{id}
      // Uncomment when API is ready
      // const response = await fetch(`/api/follow-ups/${followUp.id}`);

      // if (!response.ok) {
      //   throw new Error('Failed to fetch follow-up details');
      // }

      // const detailedFollowUp = await response.json();
      // console.log("Detailed follow-up data:", detailedFollowUp);

      // For now, simulate API call
      console.log("Simulated API call for viewing follow-up:", followUp.id);

      // Here you would typically navigate to view page or show view modal
      // For example: navigate(`/follow-ups/${followUp.id}`);
    } catch (error) {
      console.error("Error fetching follow-up details:", error);
      // Here you would typically show an error notification
    }
  };

  const handleLoadMore = () => {
    setLoadingMore(true);
    // Simulate API call
    setTimeout(() => {
      setLoadingMore(false);
      console.log("Load more follow-ups");
    }, 2000);
  };

  return (
    <PrivateLayout>
      <div className="">
        {/* Data Table */}
        <DataTable
          columns={columns}
          data={sampleFollowUps}
          searchPlaceholder="Search follow-ups..."
          addButtonText="New Follow-up"
          onView={handleView}
          actions={["cancel", "log-outcome", "reschedule"]}
          loading={loading}
          loadingMore={loadingMore}
          loadMoreText="Load More Follow-ups"
          onLoadMore={handleLoadMore}
          showLoadMore={true}
          highlightField="status"
          highlightColors={{
            Pending:
              "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400",
            Completed:
              "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",
            Canceled:
              "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400",
          }}
          // Modal forms
          createForm={({ onClose }) => (
            <FollowUpForm onClose={onClose} onSubmit={handleCreateSubmit} />
          )}
          editForm={({ item, onClose }) => (
            <RescheduleForm
              item={item}
              onClose={onClose}
              onSubmit={handleRescheduleSubmit}
            />
          )}
          deleteForm={({ item, onClose }) => (
            <DeleteConfirmation
              item={item}
              onClose={onClose}
              onConfirm={handleCancelConfirm}
              itemName="Follow-up"
              confirmText="Cancel"
              confirmButtonText="Cancel Follow-up"
              title={`Cancel Follow-up for ${item?.clientName}`}
              message="Are you sure you want to cancel this follow-up? This action cannot be undone."
            />
          )}
          logOutcomeForm={({ item, onClose }) => (
            <LogOutcomeForm
              item={item}
              onClose={onClose}
              onSubmit={handleLogOutcomeSubmit}
            />
          )}
          createModalTitle="Create New Follow-up"
          editModalTitle={
            selectedItem
              ? `Reschedule Follow-up for ${selectedItem.clientName}`
              : "Reschedule Follow-up"
          }
          deleteModalTitle=""
          logOutcomeModalTitle={
            selectedItem
              ? `Log Outcome for ${selectedItem.clientName}`
              : "Log Outcome"
          }
          modalSize="lg"
          deleteModalSize="sm"
          logOutcomeModalSize="md"
        />
      </div>
    </PrivateLayout>
  );
};

export default FollowUps;
