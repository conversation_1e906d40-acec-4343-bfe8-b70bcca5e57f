import { useState, useEffect } from "react";
import { Minus } from "lucide-react";
import PrivateLayout from "../components/layouts/PrivateLayout";
import DataTable from "../components/common/DataTable";
import CustomerForm from "../components/forms/CustomerForm";
import DeleteConfirmation from "../components/forms/DeleteConfirmation";
import {
  customersService,
  formatCustomersResponse,
  formatCustomersForTable,
} from "../services/customersService";

const Customers = () => {
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [customers, setCustomers] = useState([]);
  const [error, setError] = useState(null);

  // Define columns of the table here and what to render
  const columns = [
    {
      key: "clientId",
      title: "CLIENT ID",
      render: (value) => (
        <span className="font-bold" style={{ color: "#7e7e7e" }}>
          {value && value !== "N/A" ? (
            value
          ) : (
            <Minus size={16} className="text-gray-400" />
          )}
        </span>
      ),
    },
    {
      key: "accountNumber",
      title: "ACCOUNT NUMBER",
      render: (value) => (
        <span className="font-bold" style={{ color: "#7e7e7e" }}>
          {value && value !== "N/A" ? (
            value
          ) : (
            <Minus size={16} className="text-gray-400" />
          )}
        </span>
      ),
    },
    {
      key: "name",
      title: "NAME",
      render: (value) => (
        <span className="font-medium" style={{ color: "#7e7e7e" }}>
          {value || "Unknown Customer"}
        </span>
      ),
    },
    {
      key: "phoneNumber",
      title: "PHONE NUMBER",
      render: (value) => (
        <span className="text-sm" style={{ color: "#7e7e7e" }}>
          {value && value !== "N/A" ? (
            value
          ) : (
            <Minus size={16} className="text-gray-400" />
          )}
        </span>
      ),
    },
    {
      key: "branch",
      title: "BRANCH",
      render: (value) => (
        <span className="text-sm" style={{ color: "#7e7e7e" }}>
          {value && value !== "N/A" ? (
            value
          ) : (
            <Minus size={16} className="text-gray-400" />
          )}
        </span>
      ),
    },
  ];

  // Fetch customers from API
  const fetchCustomers = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log("Fetching customers from /customers endpoint...");

      const response = await customersService.getAll();
      console.log("Customers API response:", response);

      const formattedCustomers = formatCustomersResponse(response);
      const tableReadyCustomers = formatCustomersForTable(formattedCustomers);

      console.log("Formatted customers for table:", tableReadyCustomers);
      setCustomers(tableReadyCustomers);
    } catch (error) {
      console.error("Error fetching customers:", error);
      setError(error.response?.data?.message || "Failed to load customers");
      setCustomers([]);
    } finally {
      setLoading(false);
    }
  };

  // Fetch customers on component mount
  useEffect(() => {
    fetchCustomers();
  }, []);

  // Form submission handlers (non-functional for now)
  const handleCreateSubmit = (formData) => {
    console.log("Creating customer:", formData);
    console.log("Create functionality not implemented yet");
    // TODO: Implement customer creation API call
  };

  const handleEditSubmit = (formData) => {
    console.log("Updating customer:", formData);
    console.log("Edit functionality not implemented yet");
    // TODO: Implement customer update API call
  };

  const handleDeleteConfirm = (customer) => {
    console.log("Deleting customer:", customer);
    console.log("Delete functionality not implemented yet");
    // TODO: Implement customer deletion API call
  };

  // Import/Export handlers
  const handleExport = () => {
    console.log("Exporting customers data...");
    // Here you would typically generate and download the export file
    // For now, we'll just log the action
  };

  const handleImport = (file) => {
    console.log("Importing customers from file:", file.name);
    // Here you would typically process the uploaded file
    // Parse the data and make API calls to import customers
  };

  const handleView = (customer) => {
    console.log("View customer:", customer);
    // Here you would typically navigate to view page or show view modal
  };

  const handleLoadMore = () => {
    setLoadingMore(true);
    // Simulate API call
    setTimeout(() => {
      setLoadingMore(false);
      console.log("Load more customers");
    }, 2000);
  };

  return (
    <PrivateLayout>
      <div className="">
        {/* Data Table */}
        <DataTable
          columns={columns}
          data={customers}
          searchPlaceholder="Search customers..."
          addButtonText="New Customer"
          onView={handleView}
          actions={["edit", "delete"]}
          loading={loading}
          loadingMore={loadingMore}
          loadMoreText="Load More Customers"
          onLoadMore={handleLoadMore}
          showLoadMore={false}
          highlightField="status"
          highlightColors={{
            Active:
              "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",
            Inactive:
              "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400",
            Pending:
              "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400",
            Suspended:
              "bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400",
          }}
          // Modal forms
          createForm={({ onClose }) => (
            <CustomerForm onClose={onClose} onSubmit={handleCreateSubmit} />
          )}
          editForm={({ item, onClose }) => (
            <CustomerForm
              item={item}
              onClose={onClose}
              onSubmit={handleEditSubmit}
            />
          )}
          deleteForm={({ item, onClose }) => (
            <DeleteConfirmation
              item={item}
              onClose={onClose}
              onConfirm={handleDeleteConfirm}
              itemName="Customer"
            />
          )}
          createModalTitle="Create New Customer"
          editModalTitle="Edit Customer"
          deleteModalTitle=""
          modalSize="lg"
          deleteModalSize="sm"
          // Import/Export functionality
          showImportExport={true}
          onExport={handleExport}
          onImport={handleImport}
          importModalTitle="Import Customers"
          importTemplateFileName="Customers-Template.xlsx"
          importAcceptedFileTypes=".xlsx,.xls,.csv"
        />
      </div>
    </PrivateLayout>
  );
};

export default Customers;
