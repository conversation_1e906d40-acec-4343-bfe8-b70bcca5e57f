import { useState, useEffect } from "react";
import { useParams, useNavigate, useLocation } from "react-router-dom";
import { Plus } from "lucide-react";
import PrivateLayout from "../components/layouts/PrivateLayout";
import DataTable from "../components/common/DataTable";
import Modal from "../components/common/Modal";
import ActivityForm from "../components/forms/ActivityForm";

const GeneralActivities = () => {
  const { type } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const [loading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);

  // Default to 'calls' if no type is specified
  const activeTab = type || "calls";

  // Filter states
  const [filters, setFilters] = useState({
    madeBy: "",
    status: "",
  });

  // Date range filter states
  const [fromDate, setFromDate] = useState("");
  const [toDate, setToDate] = useState("");

  // For customer service hitlist, we don't need tabs - just show the hitlist
  // This component is now used for the customer service hitlist page

  // Tab configuration (keeping for potential future use)
  const tabs = [
    { id: "hitlist", label: "Hitlist", path: "/customer-service/hitlist" },
  ];

  // Handle tab change
  const handleTabChange = (tabId) => {
    const tab = tabs.find((t) => t.id === tabId);
    if (tab) {
      navigate(tab.path);
    }
  };

  // Filter configuration
  const filterConfig = [
    {
      key: "madeBy",
      label: "Made By",
      field: "madeBy",
      placeholder: "All Made By",
      selectedValue: filters.madeBy,
      options: [
        { value: "Michael Johnson", label: "Michael Johnson" },
        { value: "Emily Davis", label: "Emily Davis" },
        { value: "Sarah Wilson", label: "Sarah Wilson" },
        { value: "David Brown", label: "David Brown" },
        { value: "Robert Miller", label: "Robert Miller" },
      ],
    },
    {
      key: "status",
      label: "Status",
      field: "status",
      placeholder: "All Status",
      selectedValue: filters.status,
      options: [
        { value: "Completed", label: "Completed" },
        { value: "Pending", label: "Pending" },
        { value: "Scheduled", label: "Scheduled" },
        { value: "Cancelled", label: "Cancelled" },
      ],
    },
  ];

  // Filter handlers
  const handleFilterChange = (filterKey, value) => {
    setFilters((prev) => ({
      ...prev,
      [filterKey]: value,
    }));
  };

  const handleClearFilters = () => {
    setFilters({
      madeBy: "",
      status: "",
    });
  };

  // Date range handlers
  const handleFromDateChange = (date) => {
    setFromDate(date);
  };

  const handleToDateChange = (date) => {
    setToDate(date);
  };

  // Define columns based on active tab
  const getColumns = () => {
    // Common columns for both calls and visits
    return [
      {
        key: "clientId",
        title: "CLIENT ID",
        render: (value) => (
          <span className="font-medium text-gray-900 dark:text-white">
            {value}
          </span>
        ),
      },
      {
        key: "clientName",
        title: "CLIENT NAME",
        render: (value) => (
          <span className="font-medium text-gray-900 dark:text-white">
            {value}
          </span>
        ),
      },
      {
        key: "purpose",
        title: "PURPOSE",
        render: (value) => (
          <span className="text-sm text-gray-600 dark:text-gray-400">
            {value}
          </span>
        ),
      },
      {
        key: "madeBy",
        title: "MADE BY",
        render: (value) => (
          <span className="text-sm text-gray-600 dark:text-gray-400">
            {value}
          </span>
        ),
      },
      {
        key: "status",
        title: "STATUS",
        render: (value) => {
          const getStatusColor = (status) => {
            switch (status) {
              case "Completed":
                return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";
              case "Pending":
                return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400";
              case "Scheduled":
                return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400";
              case "Cancelled":
                return "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400";
              default:
                return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
            }
          };

          return (
            <span
              className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(
                value
              )}`}
            >
              {value}
            </span>
          );
        },
      },
      {
        key: "date",
        title: "DATE",
        render: (value) => (
          <span className="text-sm text-gray-600 dark:text-gray-400">
            {new Date(value).toLocaleDateString()}
          </span>
        ),
      },
    ];
  };

  // Sample data based on active tab
  const getSampleData = () => {
    if (activeTab === "calls") {
      return [
        {
          id: "call001",
          clientId: "CL001",
          clientName: "John Kamau",
          purpose: "Business Consultation",
          madeBy: "Michael Johnson",
          status: "Completed",
          date: "2024-02-20T10:00:00Z",
        },
        {
          id: "call002",
          clientId: "CL002",
          clientName: "Mary Wanjiku",
          purpose: "Loan Application Follow-up",
          madeBy: "Emily Davis",
          status: "Pending",
          date: "2024-02-21T14:00:00Z",
        },
        {
          id: "call003",
          clientId: "CL003",
          clientName: "Peter Mwangi",
          purpose: "Account Opening",
          madeBy: "Sarah Wilson",
          status: "Completed",
          date: "2024-02-22T09:00:00Z",
        },
        {
          id: "call004",
          clientId: "CL004",
          clientName: "Grace Akinyi",
          purpose: "Investment Advisory",
          madeBy: "David Brown",
          status: "Cancelled",
          date: "2024-02-19T15:30:00Z",
        },
        {
          id: "call005",
          clientId: "CL005",
          clientName: "James Ochieng",
          purpose: "Insurance Inquiry",
          madeBy: "Robert Miller",
          status: "Pending",
          date: "2024-02-23T11:00:00Z",
        },
      ];
    } else if (activeTab === "visits") {
      return [
        {
          id: "visit001",
          clientId: "CL006",
          clientName: "Alice Wanjiru",
          purpose: "Property Valuation",
          madeBy: "Michael Johnson",
          status: "Scheduled",
          date: "2024-02-25T10:00:00Z",
        },
        {
          id: "visit002",
          clientId: "CL007",
          clientName: "Samuel Kiprotich",
          purpose: "Mortgage Application",
          madeBy: "Emily Davis",
          status: "Completed",
          date: "2024-02-24T14:00:00Z",
        },
        {
          id: "visit003",
          clientId: "CL008",
          clientName: "Catherine Njeri",
          purpose: "Business Assessment",
          madeBy: "Sarah Wilson",
          status: "Scheduled",
          date: "2024-02-26T09:00:00Z",
        },
        {
          id: "visit004",
          clientId: "CL009",
          clientName: "Daniel Otieno",
          purpose: "Financial Planning",
          madeBy: "David Brown",
          status: "Cancelled",
          date: "2024-02-23T15:30:00Z",
        },
        {
          id: "visit005",
          clientId: "CL010",
          clientName: "Ruth Muthoni",
          purpose: "Loan Disbursement",
          madeBy: "Robert Miller",
          status: "Scheduled",
          date: "2024-02-27T11:00:00Z",
        },
      ];
    }
    return [];
  };

  // Form submission handlers
  const handleCreateSubmit = async (formData) => {
    console.log("Creating general activity:", formData);
    // API integration logic here
    setIsCreateModalOpen(false);
  };

  const handleEditSubmit = (formData) => {
    console.log("Updating general activity:", formData);
    // API integration logic here
  };

  const handleDeleteConfirm = async (activity) => {
    console.log("Deleting general activity:", activity);
    // API integration logic here
  };

  const handleView = async (activity) => {
    setSelectedItem(activity);
    console.log("View general activity:", activity);
    // API integration logic here
  };

  const handleLoadMore = () => {
    setLoadingMore(true);
    setTimeout(() => {
      setLoadingMore(false);
      console.log("Load more general activities");
    }, 2000);
  };

  // Custom header content with tabs and create button
  const customHeaderContent = (
    <div className="mb-4">
      <div className="flex justify-between items-center">
        {/* Tabs */}
        <div className="flex space-x-2">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => handleTabChange(tab.id)}
              className={`px-6 py-2 text-sm font-medium rounded-lg transition-colors duration-200 ${
                activeTab === tab.id
                  ? "bg-[#1c5b41] text-white shadow-sm"
                  : "bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-500"
              }`}
            >
              {tab.label}
            </button>
          ))}
        </div>

        {/* Create Activity Button */}
        <button
          onClick={() => setIsCreateModalOpen(true)}
          className="inline-flex items-center px-6 py-2 bg-[#165026] hover:bg-green-800 text-white font-medium rounded-lg transition-colors duration-200 shadow-sm"
        >
          <Plus size={20} className="mr-2" />
          Create activity
        </button>
      </div>
    </div>
  );

  return (
    <PrivateLayout>
      <div className="">
        {/* Data Table */}
        <DataTable
          columns={getColumns()}
          data={getSampleData()}
          searchPlaceholder={`Search ${activeTab}...`}
          addButtonText={`New ${
            activeTab.charAt(0).toUpperCase() + activeTab.slice(1, -1)
          }`}
          onView={handleView}
          actions={["edit", "delete"]}
          loading={loading}
          loadingMore={loadingMore}
          loadMoreText={`Load More ${
            activeTab.charAt(0).toUpperCase() + activeTab.slice(1)
          }`}
          onLoadMore={handleLoadMore}
          showLoadMore={true}
          highlightField="status"
          highlightColors={{
            Scheduled:
              "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400",
            Completed:
              "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",
            Cancelled:
              "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400",
            Pending:
              "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400",
          }}
          dataCountLabel={activeTab}
          // Custom header content
          customHeaderContent={customHeaderContent}
          // Date range filter
          showDateRangeFilter={true}
          dateRangeField="date"
          fromDate={fromDate}
          toDate={toDate}
          onFromDateChange={handleFromDateChange}
          onToDateChange={handleToDateChange}
          // Filters
          showFilters={true}
          filterConfig={filterConfig}
          onFilterChange={handleFilterChange}
          onClearFilters={handleClearFilters}
          createModalTitle={`Create New ${
            activeTab.charAt(0).toUpperCase() + activeTab.slice(1, -1)
          }`}
          editModalTitle={`Edit ${
            activeTab.charAt(0).toUpperCase() + activeTab.slice(1, -1)
          }`}
          deleteModalTitle={`Delete ${
            activeTab.charAt(0).toUpperCase() + activeTab.slice(1, -1)
          }`}
          modalSize="lg"
        />

        {/* Create Activity Modal */}
        <Modal
          isOpen={isCreateModalOpen}
          onClose={() => setIsCreateModalOpen(false)}
          title="Create New Activity"
          size="xl"
        >
          <ActivityForm
            onClose={() => setIsCreateModalOpen(false)}
            onSubmit={handleCreateSubmit}
          />
        </Modal>
      </div>
    </PrivateLayout>
  );
};

export default GeneralActivities;
