import { useState } from "react";
import PrivateLayout from "../components/layouts/PrivateLayout";
import DataTable from "../components/common/DataTable";
import DeleteConfirmation from "../components/forms/DeleteConfirmation";
import ProductForm from "../components/forms/ProductForm";


const Products = () => {
    const [loading] = useState(false);
    const [loadingMore, setLoadingMore] = useState(false);

    // Define columns of the table here and what to render
    const columns = [

        {
            key: "name",
            title: "PRODUCT",
            render: (value) => (
                <span className="font-medium text-gray-900 dark:text-white">
                    {value}
                </span>
            ),
        },

        {
            key: "category",
            title: "CATEGORY",
            render: (value) => {
                const getCategoryColor = (category) => {
                    switch (category) {
                        case "NAIROBI":
                            return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400";
                        case "CENTRAL":
                            return "bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400";
                        default:
                            return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
                    }
                };

                return (
                    <span
                        className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getCategoryColor(
                            value
                        )}`}
                    >
                        {value}
                    </span>
                );
            },
        },
        {
            key: "addedBy",
            title: "ADDED BY",
            render: (value) => (
                <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
                    {value}
                </span>
            ),
        },

        {
            key: "addedOn",
            title: "ADDED ON",
            render: (value) => (
                <span className="text-sm text-gray-600 dark:text-gray-400">
                    {value}
                </span>
            ),
        },

    ];

    // Data to be rendered in the table, should match the columns above
    const sampleProducts = [
        {
            id: "********",
            name: "Current Account",
            category: "BUSINESS BANKING",
            addedBy: "RENOIR",
            addedOn: "July 16 2025",
        },
        {
            id: "********",
            name: "Savings Account",
            category: "BUSINESS BANKING",
            addedBy: "RENOIR",
            addedOn: "July 16 2025",
        },
        {
            id: "********",
            name: "Junior Savings Account",
            category: "BUSINESS BANKING",
            addedBy: "KYLE",
            addedOn: "July 16 2025",
        },
        {
            id: "********",
            name: "Transaction Account",
            category: "BUSINESS BANKING",
            addedBy: "STACY",
            addedOn: "July 16 2025",
        },
        {
            id: "********",
            name: "Chama Account",
            category: "BUSINESS BANKING",
            addedBy: "RENOIR",
            addedOn: "July 16 2025",
        },

    ];

    // Form submission handlers
    const handleCreateSubmit = (formData) => {
        console.log("Creating product:", formData);
        // Here you would typically make an API call to create the product
        // After successful creation, you might want to refresh the data
    };

    const handleEditSubmit = (formData) => {
        console.log("Updating product:", formData);
        // Here you would typically make an API call to update the product
        // After successful update, you might want to refresh the data
    };

    const handleDeleteConfirm = (product) => {
        console.log("Deleting product:", product);
        // Here you would typically make an API call to delete the product
        // After successful deletion, you might want to refresh the data
    };

    const handleView = (product) => {
        console.log("View product:", product);
        // Here you would typically navigate to view page or show view modal
    };

    const handleLoadMore = () => {
        setLoadingMore(true);
        // Simulate API call
        setTimeout(() => {
            setLoadingMore(false);
            console.log("Load more product");
        }, 2000);
    };


    return (
        <PrivateLayout>
            {/* Data Table */}
            <div className="">
                <DataTable
                    columns={columns}
                    data={sampleProducts}
                    searchPlaceholder="Search ..."
                    addButtonText="New Product"
                    onView={handleView}
                    actions={["view", "edit", "delete"]}
                    loading={loading}
                    loadingMore={loadingMore}
                    loadMoreText="Load More Products"
                    onLoadMore={handleLoadMore}
                    showLoadMore={true}
                    highlightField="addedBy"
                    highlightColors={{
                        BUSINESS:
                            "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",
                        PERSONAL:
                            "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400",
                    }}
                    // Modal forms
                    createForm={({ onClose }) => (
                        <ProductForm onClose={onClose} onSubmit={handleCreateSubmit} />
                    )}
                    editForm={({ item, onClose }) => (
                        <ProductForm
                            item={item}
                            onClose={onClose}
                            onSubmit={handleEditSubmit}
                        />
                    )}
                    deleteForm={({ item, onClose }) => (
                        <DeleteConfirmation
                            item={item}
                            onClose={onClose}
                            onConfirm={handleDeleteConfirm}
                            itemName="Product"
                        />
                    )}
                    createModalTitle="Create New Product"
                    editModalTitle="Product"
                    deleteModalTitle=""
                    modalSize="lg"
                    deleteModalSize="sm"
                />

            </div>

        </PrivateLayout>

    )
}

export default Products;
