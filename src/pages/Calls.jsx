import { useState } from "react";
import PrivateLayout from "../components/layouts/PrivateLayout";
import DataTable from "../components/common/DataTable";
import DeleteConfirmation from "../components/forms/DeleteConfirmation";
import CallForm from "../components/forms/CallForm";
import VisitForm from "../components/forms/VisitForm";

const Calls = () => {
  const [loading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);

  // Define columns of the table here and what to render
  const columns = [
    
    {
      key: "name",
      title: "NAME",
      render: (value) => (
        <span className="font-medium text-gray-900 dark:text-white">
          {value}
        </span>
      ),
    },
    {
      key: "anchor",
      title: "ANCHOR",
      render: (value) => (
        <span className="font-medium text-gray-900 dark:text-white">
          {value}
        </span>
      ),
    },
    {
      key: "mobile",
      title: "MO<PERSON><PERSON>",
      render: (value) => (
        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
          {value}
        </span>
      ),
    },

     {
      key: "madeBy",
      title: "MADE BY",
      render: (value) => (
        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
          {value}
        </span>
      ),
    },
    {
      key: "status",
      title: "STATUS",
      render: (value) => (
        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
          {value}
        </span>
      ),
    },
   
    {
      key: "date",
      title: "DATE",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value}
        </span>
      ),
    },
   
  ];

  // Data to be rendered in the table, should match the columns above
  const sampleCalls = [
    {
      id: "00000004",
      name: "JAMAL JUNIOR",
      anchor: "RENOIR AUGUSTE",
      mobile: "0751116997",
      madeBy: "CANALETTO",
      status: "Completed",
      date: "21 July 2025",
    },

     {
      id: "00000003",
      name: "KYLE NDERITU",
      anchor: "TESH WAIRE",
      mobile: "0751116997",
      madeBy: "CANALETTO",
      status: "Completed",
      date: "21 July 2025",
    }
    ,
    {
      id: "00000002",
      name: "JAMAL JUNIOR",
      anchor: "RENOIR AUGUSTE",
      mobile: "0751116997",
      madeBy: "CANALETTO",
      status: "Completed",
      date: "21 July 2025",
    },
    {
      id: "00000001",
      name: "JAMAL JUNIOR",
      anchor: "RENOIR AUGUSTE",
      mobile: "0751116997",
      madeBy: "CANALETTO",
      status: "Completed",
      date: "21 July 2025",
    },
     {
      id: "00000000",
      name: "JAMAL JUNIOR",
      anchor: "RENOIR AUGUSTE",
      mobile: "0751116997",
      madeBy: "CANALETTO",
      status: "Completed",
      date: "21 July 2025",
    },
    
  ];

   // Form submission handlers
  const handleCreateSubmit = (formData) => {
    console.log("Creating CALL:", formData);
    // Here you would typically make an API call to create the call
    // After successful creation, you might want to refresh the data
  };

  const handleEditSubmit = (formData) => {
    console.log("Updating call:", formData);
    // Here you would typically make an API call to update the call
    // After successful update, you might want to refresh the data
  };

  const handleDeleteConfirm = (call) => {
    console.log("Deleting call:", call);
    // Here you would typically make an API call to delete the call
    // After successful deletion, you might want to refresh the data
  };

  const handleView = (call) => {
    console.log("View call:", call);
    // Here you would typically navigate to view page or show view modal
  };

  const handleLoadMore = () => {
    setLoadingMore(true);
    // Simulate API call
    setTimeout(() => {
      setLoadingMore(false);
      console.log("Load more calls");
    }, 2000);
  };

  const handleCallSubmit = (formData) => {
    console.log("Making call:", formData);
    // API call to create/log call
  };

  const handleVisitSubmit = (formData) => {
    console.log("Scheduling visit:", formData);
    // API call to create/schedule visit
  };

  return (
    <PrivateLayout>
      {/* Data Table */}
      <div className="">
        <DataTable
          columns={columns}
          data={sampleCalls}
          searchPlaceholder="Search ..."
          addButtonText="New Call"
          onView={handleView}
          actions={["view", "edit", "delete", "call", "visit", "set-pending", "set-hot", "set-warm", "set-cold", "convert-to-client"]}
          loading={loading}
          loadingMore={loadingMore}
          loadMoreText="Load More Calls"
          onLoadMore={handleLoadMore}
          showLoadMore={true}
          highlightField="addedBy"
          highlightColors={{
            BUSINESS:
              "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",
            PERSONAL:
              "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400",
          }}
          // Modal forms
          createForm={({ onClose }) => (
            <CallForm onClose={onClose} onSubmit={handleCreateSubmit} />
          )}
          editForm={({ item, onClose }) => (
            <CallForm
              item={item}
              onClose={onClose}
              onSubmit={handleEditSubmit}
            />
          )}
          deleteForm={({ item, onClose }) => (
            <DeleteConfirmation
              item={item}
              onClose={onClose}
              onConfirm={handleDeleteConfirm}
              itemName="Call"
            />
          )}
          createModalTitle="Call To"
          editModalTitle="Call"
          deleteModalTitle=""
          modalSize="sm"
          deleteModalSize="sm"
          callForm={({ item, onClose }) => (
            <CallForm
              item={item}
              onClose={onClose}
              onSubmit={handleCallSubmit}
            />
          )}
          visitForm={({ item, onClose }) => (
            <VisitForm
              item={item}
              onClose={onClose}
              onSubmit={handleVisitSubmit}
            />
          )}
          callModalTitle="Make Call"
          visitModalTitle="Schedule Visit"
          callModalSize="sm"
          visitModalSize="sm"
        />

      </div>

    </PrivateLayout>
  
  )
}

export default Calls
