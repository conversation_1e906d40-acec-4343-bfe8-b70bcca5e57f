import { useState, useEffect, useCallback } from "react";
import PrivateLayout from "../components/layouts/PrivateLayout";
import DataTable from "../components/common/DataTable";
import UserForm from "../components/forms/UserForm";
import DeleteConfirmation from "../components/forms/DeleteConfirmation";
import { useApi } from "../contexts/ApiContext";

/**
 * Users Page Component
 *
 * This component manages the users listing page with full CRUD operations.
 * It integrates with the backend API to:
 * - Fetch and display users with pagination
 * - Create new users
 * - Edit existing users
 * - Delete users
 * - Filter users by role and status
 *
 * Backend Integration:
 * - Uses userApi from ApiContext for all API operations
 * - Handles backend response format: { data: [...], meta: {...} }
 * - Maps backend fields to frontend display format
 * - Implements proper error handling with toast notifications
 *
 * Features:
 * - Real-time data fetching from /users endpoint
 * - Pagination with load more functionality
 * - Role-based filtering
 * - Responsive data table with search
 * - Modal forms for create/edit operations
 * - Confirmation dialogs for delete operations
 */
const Users = () => {
  // API context for user operations
  const { userApi } = useApi();

  // Component state
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [users, setUsers] = useState([]);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
  });

  // Dynamic filter options state
  const [roleOptions, setRoleOptions] = useState([]);
  const [loadingFilters, setLoadingFilters] = useState(false);

  // Filter states for Users page
  const [filters, setFilters] = useState({
    role: "",
    status: "",
  });

  // Dynamic filter configuration - updates when role options are loaded
  const filterConfig = [
    {
      key: "role",
      label: "Roles",
      field: "role",
      placeholder: "All Roles",
      selectedValue: filters.role,
      options: roleOptions.length > 0 ? roleOptions : [
        { value: "ADMIN", label: "Admin" },
        { value: "CUSTOMER SERVICE", label: "Customer Service" },
        { value: "MANAGER", label: "Manager" },
        { value: "USER", label: "User" },
      ],
    },
    {
      key: "status",
      label: "Status",
      field: "status",
      placeholder: "All Status",
      selectedValue: filters.status,
      options: [
        { value: "active", label: "Active" },
        { value: "inactive", label: "Inactive" },
      ],
    },
  ];

  // Load filter options from API
  const loadFilterOptions = useCallback(async () => {
    setLoadingFilters(true);
    try {
      // Load roles for filter dropdown
      const roles = await userApi.getRoles();

      // Format roles for filter dropdown (different from form dropdown)
      const formattedRoles = roles.map(role => ({
        value: role.label, // Use the role name as value for filtering
        label: role.label,
      }));

      setRoleOptions(formattedRoles);
      console.log('Loaded filter roles:', formattedRoles);
    } catch (error) {
      console.error('Error loading filter options:', error);
      // Keep default options if API fails
    } finally {
      setLoadingFilters(false);
    }
  }, [userApi]);

  // Filter handlers
  const handleFilterChange = (filterKey, value) => {
    console.log(`Filter changed: ${filterKey} = ${value}`);
    setFilters((prev) => ({
      ...prev,
      [filterKey]: value,
    }));

    // Reset pagination when filters change
    setPagination(prev => ({
      ...prev,
      page: 1,
    }));
  };

  const handleClearFilters = () => {
    console.log('Clearing all filters');
    setFilters({
      role: "",
      status: "",
    });

    // Reset pagination when filters are cleared
    setPagination(prev => ({
      ...prev,
      page: 1,
    }));
  };

  // Define columns of the table here and what to render
  const columns = [
    {
      key: "name",
      title: "NAME",
      render: (value) => (
        <span className="font-medium text-gray-900 dark:text-white">
          {value}
        </span>
      ),
    },
    {
      key: "email",
      title: "EMAIL",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value}
        </span>
      ),
    },
    {
      key: "phone",
      title: "PHONE",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value || "N/A"}
        </span>
      ),
    },
    {
      key: "rmCode",
      title: "RM CODE",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value || "N/A"}
        </span>
      ),
    },
    {
      key: "role",
      title: "ROLE",
      render: (value) => (
        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
          {value}
        </span>
      ),
    },
    {
      key: "status",
      title: "STATUS",
      render: (value) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
          value === "active"
            ? "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400"
            : "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400"
        }`}>
          {value === "active" ? "Active" : "Inactive"}
        </span>
      ),
    },
    {
      key: "branch",
      title: "BRANCH",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value}
        </span>
      ),
    },
    {
      key: "addedBy",
      title: "ADDED BY",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value}
        </span>
      ),
    },
    {
      key: "addedOn",
      title: "ADDED ON",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {new Date(value).toLocaleDateString()}
        </span>
      ),
    },
  ];

  /**
   * Fetch users data from backend with pagination and filtering
   *
   * @param {number} page - Page number to fetch (default: 1)
   * @param {number} limit - Number of items per page (default: 10)
   *
   * Backend API: GET /users
   * Expected response format:
   * {
   *   data: [
   *     {
   *       id: "uuid",
   *       name: "User Name",
   *       email: "<EMAIL>",
   *       phone_number: "+1234567890",
   *       rm_code: "RM001",
   *       role: { id: "uuid", name: "ADMIN" },
   *       branch: {
   *         id: "uuid",
   *         name: "Branch Name",
   *         region: { id: "uuid", name: "Region Name" }
   *       },
   *       created_at: "2025-07-28T12:33:48.093Z",
   *       updated_at: "2025-07-28T12:33:48.093Z",
   *       generalActivitiesCount: 0,
   *       leadsCount: 1,
   *       loanActivitiesCount: 0,
   *       scheduledVisitsCount: 0,
   *       targetsCount: 0
   *     }
   *   ],
   *   meta: {
   *     total: 3,
   *     page: 1,
   *     limit: 10,
   *     totalPages: 1,
   *     hasNextPage: false,
   *     hasPreviousPage: false
   *   }
   * }
   */
  const fetchUsers = useCallback(async (page = 1, limit = 10) => {
    setLoading(true);
    try {
      const params = {
        page,
        limit,
      };

      // Add filter parameters if they exist
      if (filters.role && filters.role.trim() !== "") {
        params.role = filters.role.trim();
        console.log('Filtering by role:', params.role);
      }

      if (filters.status && filters.status.trim() !== "") {
        params.status = filters.status.trim();
        console.log('Filtering by status:', params.status);
      }

      console.log('Fetching users with params:', params);
      const response = await userApi.getAll(params);

      // Ensure data is always an array
      const usersData = Array.isArray(response.data) ? response.data : [];

      // Apply client-side filtering as fallback if backend doesn't support all filters
      let filteredUsers = usersData;

      // Client-side role filtering (exact match or contains)
      if (filters.role && filters.role.trim() !== "") {
        const roleFilter = filters.role.trim();
        filteredUsers = filteredUsers.filter(user => {
          if (!user.role) return false;

          // Try exact match first, then contains match (case-insensitive)
          const userRole = user.role.toLowerCase();
          const filterRole = roleFilter.toLowerCase();

          return userRole === filterRole || userRole.includes(filterRole);
        });
        console.log(`Client-side role filter applied: "${roleFilter}", found ${filteredUsers.length} users`);
      }

      // Client-side status filtering
      if (filters.status && filters.status.trim() !== "") {
        const statusFilter = filters.status.trim().toLowerCase();
        filteredUsers = filteredUsers.filter(user => {
          const userStatus = user.status ? user.status.toLowerCase() : "inactive";
          return userStatus === statusFilter;
        });
        console.log(`Client-side status filter applied: "${statusFilter}", found ${filteredUsers.length} users`);
      }

      setUsers(filteredUsers);

      // Update pagination info from backend meta
      if (response.meta) {
        setPagination({
          page: response.meta.page || 1,
          limit: response.meta.limit || 10,
          total: response.meta.total || 0,
          totalPages: response.meta.totalPages || 1,
        });
      }
    } catch (error) {
      console.error("Error fetching users:", error);
      // Set empty array on error to prevent filter issues
      setUsers([]);
      // Error is handled by the API context interceptor with toast notifications
    } finally {
      setLoading(false);
    }
  }, [userApi, filters]);

  // Load filter options on component mount
  useEffect(() => {
    loadFilterOptions();
  }, [loadFilterOptions]);

  // Load users on component mount and when refresh is triggered
  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  /**
   * Form submission handlers for CRUD operations
   * All handlers integrate with backend API and refresh data on success
   */

  /**
   * Handle user creation
   * @param {Object} formData - User form data from UserForm component
   *
   * Backend API: POST /users
   * Expected payload format:
   * {
   *   name: "User Name",
   *   email: "<EMAIL>",
   *   phone_number: "+1234567890",
   *   role_id: "uuid",
   *   branch_id: "uuid",
   *   rm_code: "RM001",
   *   password: "password123"
   * }
   */
  const handleCreateSubmit = async (formData) => {
    try {
      console.log("Creating user:", formData);
      const newUser = await userApi.create(formData);

      // Add the new user to the beginning of the list instead of refreshing
      setUsers(prevUsers => [newUser, ...prevUsers]);

      // Update pagination count
      setPagination(prev => ({
        ...prev,
        total: prev.total + 1
      }));

      console.log("User created successfully and added to local state");
    } catch (error) {
      console.error("Error creating user:", error);
      // Error is handled by the API context with toast notifications
    }
  };

  /**
   * Handle user update
   * @param {Object} formData - Updated user form data
   * @param {string} userId - ID of user to update
   *
   * Backend API: PATCH /users/:id
   * Expected payload format: Same as create, but password is optional
   */
  const handleEditSubmit = async (formData, userId) => {
    try {
      console.log("Updating user:", formData, "ID:", userId);
      const updatedUser = await userApi.update(userId, formData);

      // Update the local state directly instead of refreshing the entire page
      setUsers(prevUsers =>
        prevUsers.map(user =>
          user.id === userId ? updatedUser : user
        )
      );

      console.log("User updated successfully in local state");
    } catch (error) {
      console.error("Error updating user:", error);
      // Error is handled by the API context with toast notifications
    }
  };

  /**
   * Handle user deletion
   * @param {Object} user - User object to delete
   *
   * Backend API: DELETE /users/:id
   */
  const handleDeleteConfirm = async (user) => {
    try {
      console.log("Deleting user:", user);
      await userApi.delete(user.id);

      // Remove the user from local state instead of refreshing
      setUsers(prevUsers => prevUsers.filter(u => u.id !== user.id));

      // Update pagination count
      setPagination(prev => ({
        ...prev,
        total: prev.total - 1
      }));

      console.log("User deleted successfully and removed from local state");
    } catch (error) {
      console.error("Error deleting user:", error);
      // Error is handled by the API context with toast notifications
    }
  };

  // Import/Export handlers
  const handleExport = () => {
    console.log("Exporting users data...");
    // Here you would typically generate and download the export file
    // For now, we'll just log the action
  };

  const handleImport = (file) => {
    console.log("Importing users from file:", file.name);
    // Here you would typically process the uploaded file
    // Parse the data and make API calls to import users
  };

  const handleView = (user) => {
    console.log("View user:", user);
    // Here you would typically navigate to view page or show view modal
  };

  const handleLoadMore = async () => {
    if (pagination.page >= pagination.totalPages) {
      return; // No more pages to load
    }

    setLoadingMore(true);
    try {
      const nextPage = pagination.page + 1;
      const params = {
        page: nextPage,
        limit: pagination.limit,
      };

      // Add filter parameters if they exist (same logic as fetchUsers)
      if (filters.role && filters.role.trim() !== "") {
        params.role = filters.role.trim();
      }

      if (filters.status && filters.status.trim() !== "") {
        params.status = filters.status.trim();
      }

      console.log('Loading more users with params:', params);
      const response = await userApi.getAll(params);

      // Append new users to existing ones
      const newUsers = Array.isArray(response.data) ? response.data : [];
      setUsers(prevUsers => [...prevUsers, ...newUsers]);

      // Update pagination info
      if (response.meta) {
        setPagination(prev => ({
          ...prev,
          page: response.meta.page || nextPage,
        }));
      }
    } catch (error) {
      console.error("Error loading more users:", error);
      // Error is handled by the API context interceptor
    } finally {
      setLoadingMore(false);
    }
  };

  // Check if any filters are active
  const hasActiveFilters = filters.role || filters.status;
  const activeFilterCount = [filters.role, filters.status].filter(Boolean).length;

  return (
    <PrivateLayout>
      <div className="w-full max-w-full overflow-hidden">
        {/* Filter Status Indicator */}
        {hasActiveFilters && (
          <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium text-blue-800 dark:text-blue-200">
                  {activeFilterCount} filter{activeFilterCount > 1 ? 's' : ''} active:
                </span>
                <div className="flex gap-2">
                  {filters.role && (
                    <span className="inline-flex items-center px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100 rounded-full">
                      Role: {filters.role}
                    </span>
                  )}
                  {filters.status && (
                    <span className="inline-flex items-center px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100 rounded-full">
                      Status: {filters.status}
                    </span>
                  )}
                </div>
              </div>
              <button
                onClick={handleClearFilters}
                className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200 font-medium"
              >
                Clear all
              </button>
            </div>
          </div>
        )}

        {/* Data Table - Mobile Responsive */}
        <DataTable
          columns={columns}
          data={users}
          searchPlaceholder="Search users..."
          addButtonText="New User"
          onView={handleView}
          actions={["view", "edit", "delete"]}
          loading={loading}
          loadingMore={loadingMore}
          loadMoreText="Load More Users"
          onLoadMore={handleLoadMore}
          showLoadMore={pagination.page < pagination.totalPages}
          highlightField="role"
          highlightColors={{
            "ADMIN":
              "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400",
            "CUSTOMER SERVICE":
              "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400",
            "MANAGER":
              "bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400",
            "USER":
              "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",
          }}
          // Modal forms
          createForm={({ onClose }) => (
            <UserForm
              onClose={onClose}
              onSubmit={handleCreateSubmit}
              userApi={userApi} // Pass userApi for fetching roles and branches
            />
          )}
          editForm={({ item, onClose }) => (
            <UserForm
              item={item}
              onClose={onClose}
              onSubmit={(formData) => handleEditSubmit(formData, item.id)}
              userApi={userApi} // Pass userApi for fetching roles and branches
              isEdit={true}
            />
          )}
          deleteForm={({ item, onClose }) => (
            <DeleteConfirmation
              item={item}
              onClose={onClose}
              onConfirm={handleDeleteConfirm}
              itemName="User"
            />
          )}
          createModalTitle="Create New User"
          editModalTitle="Edit User"
          deleteModalTitle=""
          deleteModalSize="sm"
          // Import/Export functionality
          showImportExport={false}
          onExport={handleExport}
          onImport={handleImport}
          importModalTitle="Import Users"
          importTemplateFileName="Users-Template.xlsx"
          importAcceptedFileTypes=".xlsx,.xls,.csv"
          // Data count and filters
          dataCountLabel="users"
          showFilters={true}
          filterConfig={filterConfig}
          onFilterChange={handleFilterChange}
          onClearFilters={handleClearFilters}
        />
      </div>
    </PrivateLayout>
  );
};

export default Users;
