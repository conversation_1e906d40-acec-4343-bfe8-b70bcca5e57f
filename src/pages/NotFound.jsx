import { useLocation, <PERSON> } from "react-router-dom";
import { useEffect } from "react";
import { Home, Shield } from "lucide-react";
import { useApp } from "../contexts/AppContext";
import error404Image from "../assets/images/error400-cover.png";

const NotFound = () => {
  const location = useLocation();
  const { darkMode } = useApp();

  useEffect(() => {}, [location.pathname]);

  return (
    <div
      className={`min-h-screen ${
        darkMode ? "bg-gray-900" : "bg-gray-50"
      } flex items-center justify-center p-4`}
    >
      <div className="text-center max-w-xl mx-auto">
        {/* 404 Illustration */}
        <div className="mb-6">
          <img
            src={error404Image}
            alt="404 Error Illustration"
            className="max-w-full h-auto mx-auto"
          />
        </div>

        {/* Error Message */}
        <div className="mb-6">
          <h2
            className={`text-[18px] md:text-[25px] font-medium ${
              darkMode ? "text-white" : "text-[#495057]"
            } mb-2`}
          >
            SORRY, PAGE NOT FOUND
          </h2>
          <p
            className={`text-sm ${
              darkMode ? "text-gray-300" : "text-[#878a99]"
            }`}
          >
            The page you are looking for is not available!
          </p>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-3 justify-center items-center">
          <Link
            to="/dashboard"
            className="w-full sm:w-auto bg-[#1c5b41] hover:bg-green-700 text-white px-5 py-2.5 rounded-lg font-medium transition-colors duration-200 flex items-center justify-center"
          >
            <Home className="w-4 h-4 mr-2" />
            Back to Dashboard
          </Link>

          <Link
            to="/login"
            className={`w-full sm:w-auto border border-[#1c5b41] text-[#1c5b41] hover:${
              darkMode ? "bg-green-900/30" : "bg-green-50"
            } px-5 py-2.5 rounded-lg font-medium transition-colors duration-200 flex items-center justify-center ${
              darkMode ? "bg-transparent" : "bg-white"
            }`}
          >
            <Shield className="w-4 h-4 mr-2" />
            Login to Account
          </Link>
        </div>
      </div>
    </div>
  );
};

export default NotFound;
