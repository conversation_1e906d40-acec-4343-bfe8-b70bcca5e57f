import { useState, useEffect, useCallback } from "react";
import PrivateLayout from "../components/layouts/PrivateLayout";
import DataTable from "../components/common/DataTable";
import DeleteConfirmation from "../components/forms/DeleteConfirmation";
import BranchForm from "../components/forms/BranchForm";
import { useApi } from "../contexts/ApiContext";

const Branches = () => {
    const { branchApi } = useApi();
    const [loading, setLoading] = useState(false);
    const [loadingMore, setLoadingMore] = useState(false);
    const [branches, setBranches] = useState([]);
    const [refreshTrigger, setRefreshTrigger] = useState(0);

    // Define columns of the table here and what to render
    const columns = [

        {
            key: "name",
            title: "BRANCH",
            render: (value) => (
                <span className="font-medium text-gray-900 dark:text-white">
                    {value}
                </span>
            ),
        },

        {
            key: "region",
            title: "REGION",
            render: (value) => {
                const getRegionColor = (region) => {
                    switch (region) {
                        case "NAIROBI":
                            return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400";
                        case "CENTRAL":
                            return "bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400";
                        default:
                            return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
                    }
                };

                return (
                    <span
                        className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getRegionColor(
                            value
                        )}`}
                    >
                        {value}
                    </span>
                );
            },
        },
        {
            key: "addedBy",
            title: "ADDED BY",
            render: (value) => (
                <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
                    {value}
                </span>
            ),
        },

        {
            key: "addedOn",
            title: "ADDED ON",
            render: (value) => (
                <span className="text-sm text-gray-600 dark:text-gray-400">
                    {value}
                </span>
            ),
        },

    ];

    // Fetch branches data
    const fetchBranches = useCallback(async () => {
        setLoading(true);
        try {
            const data = await branchApi.getAll();
            // Ensure data is always an array
            setBranches(Array.isArray(data) ? data : []);
        } catch (error) {
            console.error("Error fetching branches:", error);
            // Set empty array on error to prevent filter issues
            setBranches([]);
            // Error is handled by the API context interceptor
        } finally {
            setLoading(false);
        }
    }, [branchApi]);

    // Load branches on component mount and when refresh is triggered
    useEffect(() => {
        fetchBranches();
    }, [fetchBranches, refreshTrigger]);

    // Helper function to trigger refresh
    const refreshData = () => {
        setRefreshTrigger(prev => prev + 1);
    };

    // Form submission handlers
    const handleCreateSubmit = (result) => {
        console.log("Created branch:", result);
        // Refresh the data after successful creation
        refreshData();
    };

    const handleEditSubmit = (result) => {
        console.log("Updated branch:", result);
        // Refresh the data after successful update
        refreshData();
    };

    const handleDeleteConfirm = async (branch) => {
        try {
            await branchApi.delete(branch.id);
            console.log("Deleted branch:", branch);
            // Refresh the data after successful deletion
            refreshData();
        } catch (error) {
            console.error("Error deleting branch:", error);
            // Error is handled by the API context interceptor
        }
    };

    const handleView = (branch) => {
        console.log("View branch:", branch);
        // Here you would typically navigate to view page or show view modal
    };

    const handleLoadMore = () => {
        setLoadingMore(true);
        // Simulate API call
        setTimeout(() => {
            setLoadingMore(false);
            console.log("Load more branches");
        }, 2000);
    };


    return (
        <PrivateLayout>
            {/* Data Table */}
            <div className="">
                <DataTable
                    columns={columns}
                    data={branches}
                    searchPlaceholder="Search ..."
                    addButtonText="New Branch"
                    onView={handleView}
                    actions={["view", "edit", "delete"]}
                    loading={loading}
                    loadingMore={loadingMore}
                    loadMoreText="Load More Branches"
                    onLoadMore={handleLoadMore}
                    showLoadMore={true}
                    highlightField="addedBy"
                    highlightColors={{
                        BUSINESS:
                            "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",
                        PERSONAL:
                            "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400",
                    }}
                    // Modal forms
                    createForm={({ onClose }) => (
                        <BranchForm onClose={onClose} onSubmit={handleCreateSubmit} />
                    )}
                    editForm={({ item, onClose }) => (
                        <BranchForm
                            item={item}
                            onClose={onClose}
                            onSubmit={handleEditSubmit}
                        />
                    )}
                    deleteForm={({ item, onClose }) => (
                        <DeleteConfirmation
                            item={item}
                            onClose={onClose}
                            onConfirm={handleDeleteConfirm}
                            itemName="Branch"
                        />
                    )}
                    createModalTitle="Create New Branch"
                    editModalTitle="Branch"
                    deleteModalTitle=""
                    modalSize="lg"
                    deleteModalSize="sm"
                />

            </div>

        </PrivateLayout>

    )
}

export default Branches
