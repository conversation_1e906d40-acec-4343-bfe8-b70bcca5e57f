import { useState, useEffect } from "react";
import { Eye, Plus } from "lucide-react";
import { useNavigate, useLocation } from "react-router-dom";
import PrivateLayout from "../components/layouts/PrivateLayout";
import DataTable from "../components/common/DataTable";
import Modal from "../components/common/Modal";
import ProgressBreakdownModal from "../components/modals/ProgressBreakdownModal";
import TargetForm from "../components/forms/TargetForm";
import DeleteConfirmation from "../components/forms/DeleteConfirmation";
import { formatDateRange } from "../utils/dateUtils";
import { targetsService } from "../services/targetsService";
import { toast } from "react-toastify";

const Targets = () => {
  const navigate = useNavigate();
  const location = useLocation();

  // Get active tab from URL path
  const getActiveTabFromPath = () => {
    if (location.pathname.includes("/individual-targets")) return "individual";
    return "role"; // default to role
  };

  const [activeTab, setActiveTab] = useState(getActiveTabFromPath());
  const [loading, setLoading] = useState(true);
  const [showProgressModal, setShowProgressModal] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedTarget, setSelectedTarget] = useState(null);
  const [targets, setTargets] = useState([]);

  // Filter states
  const [filters, setFilters] = useState({
    metric: "",
    frequency: "",
  });

  // Sync activeTab with URL changes
  useEffect(() => {
    setActiveTab(getActiveTabFromPath());
  }, [location.pathname]);

  // Fetch targets on component mount
  useEffect(() => {
    fetchTargets();
  }, []);

  const fetchTargets = async () => {
    try {
      setLoading(true);
      const response = await targetsService.getAll();
      console.log("Fetched targets:", response);
      setTargets(response || []);
    } catch (error) {
      console.error("Error fetching targets:", error);
      toast.error("Failed to load targets");
      setTargets([]);
    } finally {
      setLoading(false);
    }
  };

  // Filter data based on active tab and filters
  const filteredTargets = targets.filter((target) => {
    const matchesTab =
      activeTab === "role"
        ? target.scope === "role"
        : target.scope === "individual";
    const matchesMetric =
      filters.metric === "" || target.metric === filters.metric;
    const matchesFrequency =
      filters.frequency === "" || target.frequency === filters.frequency;

    return matchesTab && matchesMetric && matchesFrequency;
  });

  // Handle tab change with navigation
  const handleTabChange = (tabKey) => {
    const path =
      tabKey === "individual"
        ? "/admin/targets/individual-targets"
        : "/admin/targets/role-targets";
    navigate(path);
    setActiveTab(tabKey);
  };

  // Custom action handler for DataTable
  const handleCustomAction = (action, row) => {
    if (action === "view-breakdown") {
      setSelectedTarget(row);
      setShowProgressModal(true);
    }
  };

  // Filter configuration for DataTable
  const filterConfig = [
    {
      key: "metric",
      label: "Metric",
      field: "metric",
      placeholder: "All Metrics",
      selectedValue: filters.metric,
      options: [
        { value: "Call", label: "Call" },
        { value: "Visit", label: "Visit" },
      ],
    },
    {
      key: "frequency",
      label: "Frequency",
      field: "frequency",
      placeholder: "All Frequencies",
      selectedValue: filters.frequency,
      options: [
        { value: "daily", label: "Daily" },
        { value: "weekly", label: "Weekly" },
        { value: "custom", label: "Custom" },
      ],
    },
  ];

  // Filter handlers
  const handleFilterChange = (filterKey, value) => {
    setFilters((prev) => ({
      ...prev,
      [filterKey]: value,
    }));
  };

  const handleClearFilters = () => {
    setFilters({
      metric: "",
      frequency: "",
    });
  };

  // Create target handler
  const handleCreateTarget = async (targetData) => {
    try {
      console.log("Creating target:", targetData);

      // Call the API service
      const response = await targetsService.create(targetData);
      console.log("Target created successfully:", response);

      // Refresh the targets list to get the latest data
      await fetchTargets();

      toast.success("Target created successfully!");
      setShowCreateModal(false);
    } catch (error) {
      console.error("Error creating target:", error);
      toast.error("Failed to create target. Please try again.");
      throw error; // Re-throw to let the form handle it
    }
  };

  // Edit target handler
  const handleEditTarget = async (targetData, targetId) => {
    try {
      console.log("Updating target:", targetId, targetData);

      // Call the API service
      const response = await targetsService.update(targetId, targetData);
      console.log("Target updated successfully:", response);

      // Refresh the targets list to get the latest data
      await fetchTargets();

      toast.success("Target updated successfully!");
      setShowEditModal(false);
      setSelectedTarget(null);
    } catch (error) {
      console.error("Error updating target:", error);
      toast.error("Failed to update target. Please try again.");
      throw error; // Re-throw to let the form handle it
    }
  };

  // Delete target handler
  const handleDeleteTarget = async (target) => {
    try {
      console.log("Deleting target:", target.id);

      // Call the API service
      await targetsService.delete(target.id);
      console.log("Target deleted successfully");

      // Remove from state instead of refetching
      setTargets((prev) => prev.filter((t) => t.id !== target.id));

      toast.success("Target deleted successfully!");
    } catch (error) {
      console.error("Error deleting target:", error);
      toast.error("Failed to delete target. Please try again.");
    }
  };

  // Define columns for the table
  const columns = [
    {
      key: "metric",
      title: "Metric",
      render: (value) => (
        <span className="font-medium text-gray-900 dark:text-white">
          {value}
        </span>
      ),
    },
    {
      key: "assigned_to",
      title: "Assigned To",
      render: (value, row) => {
        if (activeTab === "role") {
          // For role targets, just show the role name
          return (
            <span className="font-medium text-gray-900 dark:text-white">
              {typeof value === "string" ? value : value?.name || "Unknown"}
            </span>
          );
        } else {
          // For individual targets, show user name with role below in gray
          const userName = typeof value === "object" ? value?.name : value;
          const userRole =
            typeof value === "object" ? value?.role : row.assigned_to_role;

          return (
            <div className="flex flex-col">
              <span className="font-medium text-gray-900 dark:text-white">
                {userName || "Unknown User"}
              </span>
              <span className="text-xs text-gray-500">
                {userRole || "Unknown Role"}
              </span>
            </div>
          );
        }
      },
    },
    {
      key: "frequency",
      title: "Frequency",
      render: (value) => (
        <span className="capitalize text-gray-700 dark:text-gray-300">
          {value}
        </span>
      ),
    },
    {
      key: "value",
      title: "Value",
      render: (value) => (
        <span className="font-semibold text-gray-900 dark:text-white">
          {value}
        </span>
      ),
    },
    {
      key: "start_end",
      title: "Start-End",
      render: (_, row) => (
        <span className="text-sm text-gray-700 dark:text-gray-300">
          {formatDateRange(row.start_date, row.end_date)}
        </span>
      ),
    },
    {
      key: "progress",
      title: "Progress",
      render: (value, row) => {
        // Calculate progress based on metric type and API data
        const getCurrentValue = () => {
          if (row.metric === "Call") {
            return row.total_calls_made || 0;
          } else {
            return row.total_visits_made || 0;
          }
        };

        const getTargetValue = () => {
          return row.total_target || row.value || 0;
        };

        const currentValue = getCurrentValue();
        const targetValue = getTargetValue();
        const calculatedProgress =
          targetValue > 0 ? Math.round((currentValue / targetValue) * 100) : 0;

        // For individual targets, show count in brackets
        const getCountDisplay = () => {
          if (row.scope === "individual") {
            return ` (${currentValue})`;
          }
          return "";
        };

        return (
          <div className="flex items-center space-x-2">
            <div className="flex-1 bg-gray-200 rounded-full h-2 dark:bg-gray-700">
              <div
                className="bg-green-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${calculatedProgress}%` }}
              ></div>
            </div>
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300 min-w-[40px]">
              {calculatedProgress}%{getCountDisplay()}
            </span>
            <span className="text-xs text-gray-500 ml-1">
              ({currentValue}/{targetValue})
            </span>
            {/* Show eye icon only for Role Targets */}
            {activeTab === "role" && (
              <button
                onClick={() => {
                  setSelectedTarget(row);
                  setShowProgressModal(true);
                }}
                className="p-1 hover:bg-gray-100 rounded transition-colors"
                title="View Breakdown"
              >
                <Eye size={16} className="text-gray-500 hover:text-gray-700" />
              </button>
            )}
          </div>
        );
      },
    },
  ];

  // Add users count column for role targets
  if (activeTab === "role") {
    columns.splice(-1, 0, {
      key: "users_count",
      title: "Users",
      render: (value, row) => {
        const usersCount = row.users_count || 0;
        const exemptedUsers = row.exempted_users || 0;

        return (
          <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
            <span>{usersCount}</span>
            {exemptedUsers > 0 && (
              <span className="text-xs text-gray-500 ml-1">
                ({exemptedUsers} exempted)
              </span>
            )}
          </div>
        );
      },
    });
  }

  const tabs = [
    { key: "role", label: "Role Targets" },
    { key: "individual", label: "Individual Targets" },
  ];

  return (
    <PrivateLayout pageTitle="Targets">
      <div className="space-y-6">
        {/* Data Table */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
          {/* Tab Navigation with Add Target Button */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
            <div className="flex space-x-2">
              {tabs.map((tab) => (
                <button
                  key={tab.key}
                  onClick={() => handleTabChange(tab.key)}
                  className={`px-4 py-2 text-sm font-medium rounded-lg transition-colors duration-200 ${
                    activeTab === tab.key
                      ? "text-white shadow-sm"
                      : "text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
                  }`}
                  style={{
                    backgroundColor:
                      activeTab === tab.key ? "#82c355" : "#ced4da",
                  }}
                >
                  {tab.label}
                </button>
              ))}
            </div>

            {/* Add Target Button */}
            <button
              onClick={() => setShowCreateModal(true)}
              className="inline-flex items-center px-4 py-2 bg-[#165026] hover:bg-green-700 text-white transition-colors duration-200 text-sm rounded-lg"
            >
              <Plus size={16} className="mr-2" />
              Add Target
            </button>
          </div>
          <DataTable
            columns={columns}
            data={filteredTargets}
            searchPlaceholder="Search targets..."
            addButtonText="New Target"
            actions={
              activeTab === "role"
                ? ["view-breakdown", "edit", "delete"]
                : ["edit", "delete"]
            }
            loading={loading}
            showDataCount={true}
            dataCountLabel="targets"
            // Filters
            showFilters={true}
            filterConfig={filterConfig}
            onFilterChange={handleFilterChange}
            onClearFilters={handleClearFilters}
            // Custom actions
            onCustomAction={handleCustomAction}
            customActionLabels={{
              "view-breakdown": "View Breakdown",
            }}
            // Modal forms
            editForm={({ item, onClose }) => (
              <TargetForm
                item={item}
                onClose={onClose}
                onSubmit={handleEditTarget}
              />
            )}
            deleteForm={({ item, onClose }) => (
              <DeleteConfirmation
                item={item}
                onClose={onClose}
                onConfirm={handleDeleteTarget}
                itemName="target"
                confirmText="delete"
                title="Delete Target"
                message="Are you sure you want to delete this target? This action cannot be undone."
              />
            )}
            editModalTitle="Edit Target"
            deleteModalTitle=""
            modalSize="lg"
            deleteModalSize="sm"
          />
        </div>

        {/* Create Target Modal */}
        <Modal
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          title="Create New Target"
          size="lg"
        >
          <TargetForm
            onClose={() => setShowCreateModal(false)}
            onSubmit={handleCreateTarget}
          />
        </Modal>

        {/* Progress Breakdown Modal */}
        <ProgressBreakdownModal
          isOpen={showProgressModal}
          onClose={() => {
            setShowProgressModal(false);
            setSelectedTarget(null);
          }}
          target={selectedTarget}
          onTargetUpdate={(newTarget, removedUser, roleTarget) => {
            // Add the new individual target to the targets state
            setTargets((prev) => {
              const updatedTargets = [...prev, newTarget];

              // Update the role target by subtracting the removed user's values
              const roleTargetIndex = updatedTargets.findIndex(
                (t) => t.id === roleTarget.id
              );
              if (roleTargetIndex !== -1) {
                const currentRoleTarget = updatedTargets[roleTargetIndex];
                const userCurrentValue =
                  roleTarget.metric === "Call"
                    ? removedUser.calls_made || 0
                    : removedUser.visits_made || 0;

                updatedTargets[roleTargetIndex] = {
                  ...currentRoleTarget,
                  users_count: (currentRoleTarget.users_count || 1) - 1,
                  exempted_users: (currentRoleTarget.exempted_users || 0) + 1,
                  total_target:
                    (currentRoleTarget.total_target || 0) -
                    (removedUser.target || 0),
                  total_calls_made:
                    roleTarget.metric === "Call"
                      ? (currentRoleTarget.total_calls_made || 0) -
                        userCurrentValue
                      : currentRoleTarget.total_calls_made,
                  total_visits_made:
                    roleTarget.metric === "Visit"
                      ? (currentRoleTarget.total_visits_made || 0) -
                        userCurrentValue
                      : currentRoleTarget.total_visits_made,
                };
              }

              return updatedTargets;
            });
          }}
        />
      </div>
    </PrivateLayout>
  );
};

export default Targets;
