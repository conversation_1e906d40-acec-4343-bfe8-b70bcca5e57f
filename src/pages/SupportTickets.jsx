import { useState } from "react";
import PrivateLayout from "../components/layouts/PrivateLayout";
import DataTable from "../components/common/DataTable";
import SupportTicketForm from "../components/forms/SupportTicketForm";
import DeleteConfirmation from "../components/forms/DeleteConfirmation";

const SupportTickets = () => {
  const [loading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);

  // Define columns of the table here and what to render
  const columns = [
    {
      key: "ticketId",
      title: "TICKET ID",
      render: (value) => (
        <span className="font-medium text-gray-900 dark:text-white">
          #{value}
        </span>
      ),
    },
    {
      key: "type",
      title: "TYPE",
      render: (value) => (
        <span
          className={`inline-flex px-2 py-1 text-sm font-semibold rounded-full `}
        >
          {value}
        </span>
      ),
    },
    {
      key: "priority",
      title: "PRIORITY",
      render: (value) => (
        <span
          className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
            value === "High"
              ? "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400"
              : value === "Medium"
              ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400"
              : "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400"
          }`}
        >
          {value} {value === "High" && "(24Hrs)"}{" "}
          {value === "Medium" && "(48Hrs)"} {value === "Low" && "(72Hrs)"}
        </span>
      ),
    },
    {
      key: "title",
      title: "TITLE",
      render: (value) => (
        <span className="text-sm text-gray-900 dark:text-white font-medium">
          {value}
        </span>
      ),
    },
    // {
    //   key: "description",
    //   title: "DESCRIPTION",
    //   render: (value) => (
    //     <span className="text-sm text-gray-600 dark:text-gray-400">
    //       {value.length > 50 ? `${value.substring(0, 50)}...` : value}
    //     </span>
    //   ),
    // },
    {
      key: "status",
      title: "STATUS",
      render: (value) => (
        <span
          className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
            value === "Open"
              ? "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400"
              : value === "In Progress"
              ? "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400"
              : value === "Resolved"
              ? "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400"
              : "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400"
          }`}
        >
          {value}
        </span>
      ),
    },
  ];

  // Test data of 7 support tickets
  const sampleTickets = [
    {
      id: "ticket001",
      ticketId: "ST-2025-001",
      type: "Report a bug",
      priority: "High",
      title: "Login page not loading properly",
      description:
        "The login page takes too long to load and sometimes shows a blank screen. This is affecting user experience significantly.",
      status: "Open",
      createdAt: "2025-01-15",
    },
    {
      id: "ticket002",
      ticketId: "ST-2025-002",
      type: "New feature request",
      priority: "Medium",
      title: "Add dark mode toggle in settings",
      description:
        "Users are requesting a dark mode option in the application settings for better user experience during night time usage.",
      status: "In Progress",
      createdAt: "2025-01-14",
    },
    {
      id: "ticket003",
      ticketId: "ST-2025-003",
      type: "Report a bug",
      priority: "Low",
      title: "Minor UI alignment issue in dashboard",
      description:
        "Some elements in the dashboard are slightly misaligned on mobile devices. It doesn't affect functionality but looks unprofessional.",
      status: "Open",
      createdAt: "2025-01-13",
    },
    {
      id: "ticket004",
      ticketId: "ST-2025-004",
      type: "New feature request",
      priority: "High",
      title: "Export data functionality needed",
      description:
        "Users need the ability to export their data in CSV and PDF formats for reporting purposes. This is a critical business requirement.",
      status: "In Progress",
      createdAt: "2025-01-12",
    },
    {
      id: "ticket005",
      ticketId: "ST-2025-005",
      type: "Report a bug",
      priority: "Medium",
      title: "Email notifications not working",
      description:
        "Users are not receiving email notifications for important updates. The email service seems to be having issues.",
      status: "Resolved",
      createdAt: "2025-01-11",
    },
    {
      id: "ticket006",
      ticketId: "ST-2025-006",
      type: "New feature request",
      priority: "Low",
      title: "Add search filters for better navigation",
      description:
        "It would be helpful to have advanced search filters to help users find information more quickly and efficiently.",
      status: "Open",
      createdAt: "2025-01-10",
    },
    {
      id: "ticket007",
      ticketId: "ST-2025-007",
      type: "Report a bug",
      priority: "High",
      title: "Data synchronization issues",
      description:
        "There are synchronization problems between different modules causing data inconsistency. This needs immediate attention.",
      status: "Closed",
      createdAt: "2025-01-09",
    },
  ];

  // Form submission handlers
  const handleCreateSubmit = (formData) => {
    console.log("Creating support ticket:", formData);
    // Here you would typically make an API call to create the ticket
    // After successful creation, you might want to refresh the data
  };

  const handleEditSubmit = (formData) => {
    console.log("Updating support ticket:", formData);
    // Here you would typically make an API call to update the ticket
    // After successful update, you might want to refresh the data
  };

  const handleDeleteConfirm = (ticket) => {
    console.log("Deleting support ticket:", ticket);
    // Here you would typically make an API call to delete the ticket
    // After successful deletion, you might want to refresh the data
  };

  const handleView = (ticket) => {
    console.log("View support ticket:", ticket);
    // Here you would typically navigate to view page or show view modal
  };

  const handleLoadMore = () => {
    setLoadingMore(true);
    // Simulate API call
    setTimeout(() => {
      setLoadingMore(false);
      console.log("Load more support tickets");
    }, 2000);
  };

  return (
    <PrivateLayout>
      <div className="">
        {/* Data Table */}
        <DataTable
          columns={columns}
          data={sampleTickets}
          searchPlaceholder="Search tickets..."
          addButtonText="New Ticket"
          onView={handleView}
          actions={["view", "edit", "delete"]}
          loading={loading}
          loadingMore={loadingMore}
          loadMoreText="Load More Tickets"
          onLoadMore={handleLoadMore}
          showLoadMore={true}
          highlightField="priority"
          highlightColors={{
            High: "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400",
            Medium:
              "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400",
            Low: "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",
          }}
          // Modal forms
          createForm={({ onClose }) => (
            <SupportTicketForm
              onClose={onClose}
              onSubmit={handleCreateSubmit}
            />
          )}
          editForm={({ item, onClose }) => (
            <SupportTicketForm
              item={item}
              onClose={onClose}
              onSubmit={handleEditSubmit}
            />
          )}
          deleteForm={({ item, onClose }) => (
            <DeleteConfirmation
              item={item}
              onClose={onClose}
              onConfirm={handleDeleteConfirm}
              itemName="Support Ticket"
            />
          )}
          createModalTitle="Create New Support Ticket"
          editModalTitle="Edit Support Ticket"
          modalSize="lg"
          deleteModalTitle=""
          deleteModalSize="sm"
        />
      </div>
    </PrivateLayout>
  );
};

export default SupportTickets;
