import { useState, useEffect } from 'react';

/**
 * Custom hook for animating numbers from 0 to target value
 * @param {number} targetValue - The final value to count up to
 * @param {number} duration - Animation duration in milliseconds (default: 2000)
 * @param {number} delay - Delay before starting animation in milliseconds (default: 0)
 * @returns {number} - Current animated value
 */
function useCountUp(targetValue, duration = 2000, delay = 0) {
  const [currentValue, setCurrentValue] = useState(0);

  useEffect(() => {
    if (targetValue === 0) {
      setCurrentValue(0);
      return;
    }

    const startTime = Date.now() + delay;
    const endTime = startTime + duration;

    const timer = setInterval(() => {
      const now = Date.now();
      
      if (now < startTime) {
        return; // Still in delay period
      }
      
      if (now >= endTime) {
        setCurrentValue(targetValue);
        clearInterval(timer);
        return;
      }

      // Calculate progress (0 to 1)
      const progress = (now - startTime) / duration;
      
      // Use easeOutCubic for smooth deceleration
      const easeOutCubic = 1 - Math.pow(1 - progress, 3);
      
      // Calculate current value
      const value = Math.floor(easeOutCubic * targetValue);
      setCurrentValue(value);
    }, 16); // ~60fps

    return () => clearInterval(timer);
  }, [targetValue, duration, delay]);

  return currentValue;
}

export default useCountUp;
