import instance from "../axios/instance";

// Targets Service
export const targetsService = {
  // Get all targets
  getAll: async () => {
    try {
      const response = await instance.get("/targets");
      console.log(response.data)
      return response.data;
    } catch (error) {
      console.error("Error fetching targets:", error);
      throw error;
    }
  },

  // Create a new target
  create: async (targetData) => {
    try {
      console.log("Creating target with data:", targetData);
      const response = await instance.post("/targets", targetData);
      return response.data;
    } catch (error) {
      console.error("Error creating target:", error);
      throw error;
    }
  },

  // Update an existing target
  update: async (id, targetData) => {
    try {
      console.log("Updating target with ID:", id, "Data:", targetData);
      const response = await instance.patch(`/targets/${id}`, targetData);
      return response.data;
    } catch (error) {
      console.error("Error updating target:", error);
      throw error;
    }
  },

  // Delete a target
  delete: async (id) => {
    try {
      const response = await instance.delete(`/targets/${id}`);
      return response.status === 204;
    } catch (error) {
      console.error("Error deleting target:", error);
      throw error;
    }
  },

  // Get a single target by ID
  getById: async (id) => {
    try {
      const response = await instance.get(`/targets/${id}`);
      return response.data;
    } catch (error) {
      console.error("Error fetching target by ID:", error);
      throw error;
    }
  },
};

export default targetsService;
