import instance from '../axios/instance.jsx';

// Customer Categories API endpoints
const ENDPOINTS = {
  CUSTOMER_CATEGORIES: '/customer-categories',
  CUSTOMER_CATEGORY_BY_ID: (id) => `/customer-categories/${id}`,
};

// Customer Categories Service
export const customerCategoriesService = {
  // Get all customer categories
  getAll: async () => {
    try {
      const response = await instance.get(ENDPOINTS.CUSTOMER_CATEGORIES);
      return response.data;
    } catch (error) {
      console.error('Error fetching customer categories:', error);
      throw error;
    }
  },

  // Create a new customer category
  create: async (categoryData) => {
    try {
      const response = await instance.post(ENDPOINTS.CUSTOMER_CATEGORIES, categoryData);
      return response.data;
    } catch (error) {
      console.error('Error creating customer category:', error);
      throw error;
    }
  },

  // Update an existing customer category
  update: async (id, categoryData) => {
    try {
      const response = await instance.patch(ENDPOINTS.CUSTOMER_CATEGORY_BY_ID(id), categoryData);
      return response.data;
    } catch (error) {
      console.error('Error updating customer category:', error);
      throw error;
    }
  },

  // Delete a customer category
  delete: async (id) => {
    try {
      const response = await instance.delete(ENDPOINTS.CUSTOMER_CATEGORY_BY_ID(id));
      return response.status === 204;
    } catch (error) {
      console.error('Error deleting customer category:', error);
      throw error;
    }
  },

  // Get a single customer category by ID
  getById: async (id) => {
    try {
      const response = await instance.get(ENDPOINTS.CUSTOMER_CATEGORY_BY_ID(id));
      return response.data;
    } catch (error) {
      console.error('Error fetching customer category by ID:', error);
      throw error;
    }
  },
};

// Data formatter functions
export const formatCategoryData = (category) => {
  return {
    id: category.id,
    name: category.name,
    description: category.description || 'N/A',
    addedOnDate: category.addedOnDate,
    addedBy: category.addedBy || 'N/A',
    // Format date for display
    formattedDate: formatDateTime(category.addedOnDate),
  };
};

export const formatDateTime = (dateString) => {
  if (!dateString) return 'N/A';

  try {
    const date = new Date(dateString);
    return date.toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true,
    });
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'Invalid Date';
  }
};

export const formatCategoriesResponse = (response) => {
  if (!response || !response.data) {
    return [];
  }

  return response.data.map(formatCategoryData);
};

export default customerCategoriesService;
