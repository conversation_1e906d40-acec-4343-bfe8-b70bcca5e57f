import instance from "../axios/instance";

// Purposes service for API calls
export const purposesService = {
  // Get all purposes
  getAll: async () => {
    try {
      const response = await instance.get("/purposes");
      console.log(response.data)
      return response.data;
    } catch (error) {
      console.error("Error fetching purposes:", error);
      throw error;
    }
  },

  // Get purpose by ID
  getById: async (id) => {
    try {
      const response = await instance.get(`/purposes/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching purpose ${id}:`, error);
      throw error;
    }
  },

  // Create new purpose
  create: async (purposeData) => {
    try {
      const response = await instance.post("/purposes", purposeData);
      return response.data;
    } catch (error) {
      console.error("Error creating purpose:", error);
      throw error;
    }
  },

  // Update purpose
  update: async (id, purposeData) => {
    try {
      const response = await instance.put(`/purposes/${id}`, purposeData);
      return response.data;
    } catch (error) {
      console.error(`Error updating purpose ${id}:`, error);
      throw error;
    }
  },

  // Delete purpose
  delete: async (id) => {
    try {
      const response = await instance.delete(`/purposes/${id}`);
      return response.status === 204;
    } catch (error) {
      console.error(`Error deleting purpose ${id}:`, error);
      throw error;
    }
  },
};

// Format purposes response for consistent data structure
export const formatPurposesResponse = (response) => {
  console.log("Raw purposes response:", response);

  if (!response) {
    console.warn("Invalid purposes response format:", response);
    return [];
  }


  // Handle different response formats
  // If response.data exists and is an array, use it
  // If response itself is an array, use it directly
  // Otherwise try response.data.data
  let purposesData;
  if (Array.isArray(response)) {
    purposesData = response;
  } else if (response.data && Array.isArray(response.data)) {
    purposesData = response.data;
  } else if (response.data && response.data.data && Array.isArray(response.data.data)) {
    purposesData = response.data.data;
  } else {
    console.warn("Could not find purposes array in response:", response);
    return [];
  }

  console.log("Extracted purposes data:", purposesData);

  return purposesData.map((purpose) => ({
    id: purpose.id,
    name: purpose.name || purpose.purpose_name || "Unknown Purpose",
    description: purpose.description || "",
    code: purpose.code || purpose.purpose_code || "",
    isActive: purpose.is_active !== undefined ? purpose.is_active : true,
    createdAt: purpose.created_at || purpose.createdAt,
    updatedAt: purpose.updated_at || purpose.updatedAt,
  }));
};
