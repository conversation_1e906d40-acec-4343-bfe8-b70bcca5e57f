import instance from '../axios/instance.jsx';

// Leads API endpoints
const ENDPOINTS = {
  LEADS: '/leads',
  LEADS_CREATE: '/leads',
  LEAD_BY_ID: (id) => `/leads/${id}`,
  LEAD_DETAILS: (id) => `/leads/${id}`,
};

// Leads Service
export const leadsService = {
  // Get all leads
  getAll: async () => {
    try {
      const response = await instance.get(ENDPOINTS.LEADS);
      return response.data;
    } catch (error) {
      console.error('Error fetching leads:', error);
      throw error;
    }
  },

  // Create a new lead
  create: async (leadData) => {
    try {
      const response = await instance.post(ENDPOINTS.LEADS_CREATE, leadData);
      return response.data;
    } catch (error) {
      console.error('Error creating lead:', error);
      throw error;
    }
  },

  // Update an existing lead
  update: async (id, leadData) => {
    try {
      const response = await instance.patch(ENDPOINTS.LEAD_BY_ID(id), leadData);
      return response.data;
    } catch (error) {
      console.error('Error updating lead:', error);
      throw error;
    }
  },

  // Update lead status specifically
  updateStatus: async (id, status) => {
    try {
      console.log(`=== UPDATING LEAD STATUS ===`);
      console.log(`Lead ID: ${id}`);
      console.log(`New Status: ${status}`);
      console.log(`API Endpoint: PATCH ${ENDPOINTS.LEAD_BY_ID(id)}`);

      const response = await instance.patch(ENDPOINTS.LEAD_BY_ID(id), {
        leadStatus: status
      });

      console.log('Status update response:', response.data);
      console.log('==============================');

      return response.data;
    } catch (error) {
      console.error('Error updating lead status:', error);

      // Handle different error types
      if (error.response?.status === 404) {
        throw new Error('Lead not found. It may have been deleted.');
      } else if (error.response?.status === 403) {
        throw new Error('You do not have permission to update this lead.');
      } else if (error.response?.status === 400) {
        throw new Error(error.response?.data?.message || 'Invalid status value.');
      } else if (error.response?.status >= 500) {
        throw new Error('Server error. Please try again later.');
      } else {
        throw new Error(error.response?.data?.message || 'Failed to update lead status.');
      }
    }
  },

  // Delete a lead
  delete: async (id) => {
    try {
      const response = await instance.delete(ENDPOINTS.LEAD_BY_ID(id));
      return response.status === 204;
    } catch (error) {
      console.error('Error deleting lead:', error);
      throw error;
    }
  },

  // Get a single lead by ID
  getById: async (id) => {
    try {
      const response = await instance.get(ENDPOINTS.LEAD_BY_ID(id));
      return response.data;
    } catch (error) {
      console.error('Error fetching lead by ID:', error);
      throw error;
    }
  },

  // Get detailed lead information for editing
  getDetails: async (id) => {
    try {
      const response = await instance.get(ENDPOINTS.LEAD_DETAILS(id));
      return response.data;
    } catch (error) {
      console.error('Error fetching lead details:', error);
      throw error;
    }
  },

  // Import leads from file with anchor assignment
  importFromFile: async (file, anchorId) => {
    try {
      // Create FormData object
      const formData = new FormData();
      formData.append('file', file);
      formData.append('anchorId', anchorId);

      // Console log the actual data being sent
      console.log('=== IMPORT DATA BEING SENT TO BACKEND ===');
      console.log('File Details:');
      console.log('  - Name:', file.name);
      console.log('  - Size:', file.size, 'bytes');
      console.log('  - Type:', file.type);
      console.log('  - Last Modified:', new Date(file.lastModified));
      console.log('Anchor ID:', anchorId);
      console.log('FormData Contents:');

      // Log FormData entries
      for (let [key, value] of formData.entries()) {
        if (key === 'file') {
          console.log(`  - ${key}:`, {
            name: value.name,
            size: value.size,
            type: value.type,
            lastModified: value.lastModified
          });
        } else {
          console.log(`  - ${key}:`, value);
        }
      }

      console.log('API Endpoint: POST /leads/upload-excel');
      console.log('Content-Type: multipart/form-data');
      console.log('==========================================');

      // Send FormData to backend
      const response = await instance.post('/leads/upload-excel', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      return response.data;
    } catch (error) {
      console.error('Error importing leads from file:', error);
      throw error;
    }
  },

  // Get leads that can serve as anchors (for anchor selection)
  getAnchors: async () => {
    try {
      // First try the dedicated anchors endpoint
      const response = await instance.get('/leads/anchors');
      return response.data;
    } catch (error) {
      console.warn('Anchors endpoint not available, falling back to all leads:', error.response?.data?.message);
      // Fallback: get all leads and use them as potential anchors
      try {
        const allLeadsResponse = await instance.get(ENDPOINTS.LEADS);
        console.log('Using all leads as anchors fallback');
        return allLeadsResponse.data;
      } catch (fallbackError) {
        console.error('Error fetching leads for anchors fallback:', fallbackError);
        throw new Error('Unable to fetch anchors. Please check your connection and try again.');
      }
    }
  },

  // Export leads to Excel file
  exportToExcel: async (searchQuery = '', onProgress = null) => {
    try {
      console.log('=== EXPORTING LEADS TO EXCEL ===');
      console.log('Search query:', searchQuery);
      console.log('API Endpoint: GET /leads/export');

      // Show progress
      if (onProgress) onProgress(30, 'Sending export request...');

      // Prepare request config
      const config = {
        responseType: 'blob', // Important for file downloads
        timeout: 300000, // 5 minutes timeout for large exports
      };

      // Add search query if provided
      if (searchQuery && searchQuery.trim()) {
        config.params = { search: searchQuery.trim() };
        console.log('Export with search filter:', searchQuery.trim());
      } else {
        console.log('Export all leads (no filter)');
      }

      // Show progress
      if (onProgress) onProgress(50, 'Processing export...');

      // Make the API call
      const response = await instance.get('/leads/export', config);

      console.log('Export response headers:', response.headers);
      console.log('Export response size:', response.data.size, 'bytes');
      console.log('=====================================');

      // Show progress
      if (onProgress) onProgress(70, 'Export completed, preparing download...');

      return response;
    } catch (error) {
      console.error('Error exporting leads:', error);

      // Handle different error types
      if (error.code === 'ECONNABORTED') {
        throw new Error('Export timeout. Please try again or contact support for large datasets.');
      } else if (error.response?.status === 404) {
        throw new Error('Export endpoint not found. Please contact support.');
      } else if (error.response?.status === 403) {
        throw new Error('You do not have permission to export leads.');
      } else if (error.response?.status >= 500) {
        throw new Error('Server error during export. Please try again later.');
      } else {
        throw new Error(error.response?.data?.message || 'Failed to export leads. Please try again.');
      }
    }
  },
};

// Data formatter functions
export const formatLeadData = (lead) => {
  return {
    id: lead.id,
    customerName: lead.customerName,
    phoneNumber: lead.phoneNumber,
    customerCategoryId: lead.customerCategoryId,
    isicSectorId: lead.isicSectorId,
    leadType: lead.leadType,
    clientId: lead.clientId || '',
    branchId: lead.branchId,
    contactPersonName: lead.contactPersonName || '',
    contactPersonPhone: lead.contactPersonPhone || '',
    employerId: lead.employerId || '',
    createdDate: lead.createdDate,
    addedBy: lead.addedBy || 'N/A',
    // Format date for display
    formattedDate: formatDateTime(lead.createdDate),
  };
};

export const formatDateTime = (dateString) => {
  if (!dateString) return 'N/A';

  try {
    const date = new Date(dateString);
    return date.toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true,
    });
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'Invalid Date';
  }
};

export const formatLeadsResponse = (response) => {
  if (!response || !response.data) {
    return [];
  }

  return response.data.map(formatLeadData);
};

// Format leads from API response for table display
export const formatLeadsForTable = (apiResponse) => {
  if (!apiResponse || !apiResponse.data) {
    return [];
  }

  // Helper function to capitalize status to match existing color definitions
  const formatStatus = (status) => {
    if (!status) return "Pending";
    return status.charAt(0).toUpperCase() + status.slice(1).toLowerCase();
  };

  return apiResponse.data.map((lead, index) => ({
    id: lead.id || `lead${index + 1}`, // Use API ID if available, fallback to generated ID
    anchor: lead.parent_lead_name || "N/A",
    name: lead.lead_name,
    phoneNumber: lead.phoneNumber,
    visits: lead.no_of_visits,
    calls: lead.no_of_calls,
    lastInteraction: lead.last_interaction,
    lastInteractionType: lead.last_interaction ? "call" : null, // Default assumption
    status: formatStatus(lead.status), // Capitalize to match existing color definitions
    // Anchor relationship fields for edit form
    anchor_relationship_id: lead.anchor_relationship_id,
    anchor_relationship_name: lead.anchor_relationship_name,
    parent_lead_id: lead.parent_lead_id,
    // Employer name field for edit form
    employerName: lead.employerName,
    officer: lead.officer,
    // Additional fields for form compatibility
    category: "N/A", // Not provided by API
    branchName: "N/A", // Not provided by API
    type: "New", // Default assumption
    isicSector: "N/A", // Not provided by API
    clientId: "",
    contactPersonName: "",
    contactPersonPhone: "",
    // Profile specific fields
    email: "N/A",
    age: "N/A",
    gender: "N/A",
    region: "N/A",
    hitListGroup: "N/A",
  }));
};

export default leadsService;
