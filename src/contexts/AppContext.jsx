import { createContext, useContext, useState, useEffect } from "react";

// Create the context
const AppContext = createContext();

// Custom hook to use the app context
export const useApp = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error("useApp must be used within an AppProvider");
  }
  return context;
};

// App provider component
export const AppProvider = ({ children }) => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [sidebarOpen, setSidebarOpen] = useState(false);

  // Theme state management
  const [themePreference, setThemePreference] = useState(() => {
    // Get user preference from localStorage, default to 'system'
    if (typeof window !== "undefined") {
      return localStorage.getItem("themePreference") || "system";
    }
    return "system";
  });

  const [darkMode, setDarkMode] = useState(() => {
    if (typeof window !== "undefined") {
      const preference = localStorage.getItem("themePreference") || "system";
      if (preference === "system") {
        return window.matchMedia("(prefers-color-scheme: dark)").matches;
      }
      return preference === "dark";
    }
    return false;
  });

  // Listen for system theme changes
  useEffect(() => {
    if (typeof window === "undefined") return;

    const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");

    const handleSystemThemeChange = (e) => {
      if (themePreference === "system") {
        setDarkMode(e.matches);
      }
    };

    mediaQuery.addEventListener("change", handleSystemThemeChange);

    return () => {
      mediaQuery.removeEventListener("change", handleSystemThemeChange);
    };
  }, [themePreference]);

  // Apply dark mode to document and save preference
  useEffect(() => {
    if (darkMode) {
      document.documentElement.classList.add("dark");
    } else {
      document.documentElement.classList.remove("dark");
    }
  }, [darkMode]);

  // Save theme preference to localStorage
  useEffect(() => {
    if (typeof window !== "undefined") {
      localStorage.setItem("themePreference", themePreference);
    }
  }, [themePreference]);

  // Update darkMode when themePreference changes
  useEffect(() => {
    if (themePreference === "system") {
      const systemDark = window.matchMedia(
        "(prefers-color-scheme: dark)"
      ).matches;
      setDarkMode(systemDark);
    } else {
      setDarkMode(themePreference === "dark");
    }
  }, [themePreference]);

  // Detect mobile screen size
  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 768);
      if (window.innerWidth < 768) {
        setSidebarCollapsed(true);
      }
    };

    // Initial check
    checkIsMobile();

    // Add event listener for window resize
    window.addEventListener("resize", checkIsMobile);

    // Cleanup
    return () => window.removeEventListener("resize", checkIsMobile);
  }, []);

  const toggleSidebar = () => {
    if (isMobile) {
      setSidebarOpen((prev) => !prev);
    } else {
      setSidebarCollapsed((prev) => !prev);
    }
  };

  const closeMobileSidebar = () => {
    if (isMobile) {
      setSidebarOpen(false);
    }
  };

  const toggleDarkMode = () => {
    if (themePreference === "system") {
      // If currently system, switch to opposite of current system preference
      const systemDark = window.matchMedia(
        "(prefers-color-scheme: dark)"
      ).matches;
      const newPreference = systemDark ? "light" : "dark";
      setThemePreference(newPreference);
      setDarkMode(newPreference === "dark");
    } else {
      // If currently manual, toggle between light/dark
      const newPreference = themePreference === "dark" ? "light" : "dark";
      setThemePreference(newPreference);
      setDarkMode(newPreference === "dark");
    }
  };

  const setSystemTheme = () => {
    setThemePreference("system");
    const systemDark = window.matchMedia(
      "(prefers-color-scheme: dark)"
    ).matches;
    setDarkMode(systemDark);
  };

  const value = {
    sidebarCollapsed,
    setSidebarCollapsed,
    toggleSidebar,
    isMobile,
    sidebarOpen,
    setSidebarOpen,
    closeMobileSidebar,
    darkMode,
    setDarkMode,
    toggleDarkMode,
    themePreference,
    setThemePreference,
    setSystemTheme,
  };

  return <AppContext.Provider value={value}>{children}</AppContext.Provider>;
};

export default AppContext;
