import { createContext, useContext } from "react";
import axios from "axios";
import { toast } from "react-toastify";
import { formatDateDisplay } from "../utils/dateUtils";

// Create the context
const ApiContext = createContext();

// Custom hook to use the API context
export const useApi = () => {
  const context = useContext(ApiContext);
  if (!context) {
    throw new Error("useApi must be used within an ApiProvider");
  }
  return context;
};

// Create axios instance with base configuration
const createApiInstance = () => {
  const baseURL = import.meta.env.VITE_API_BASE_URL || "http://localhost:5173/api/v1";
  
  const instance = axios.create({
    baseURL,
    timeout: 10000,
    headers: {
      "Content-Type": "application/json",
    },
  });

  // Request interceptor
  instance.interceptors.request.use(
    (config) => {
      // You can add auth tokens here if needed
      // const token = localStorage.getItem('authToken');
      // if (token) {
      //   config.headers.Authorization = `Bearer ${token}`;
      // }
      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );

  // Response interceptor
  instance.interceptors.response.use(
    (response) => {
      return response;
    },
    (error) => {
      // Handle common errors
      if (error.response) {
        const { status, data } = error.response;
        
        switch (status) {
          case 400:
            toast.error(data.message || "Bad request");
            break;
          case 401:
            toast.error("Unauthorized access");
            break;
          case 403:
            toast.error("Access forbidden");
            break;
          case 404:
            toast.error("Resource not found");
            break;
          case 500:
            toast.error("Internal server error");
            break;
          default:
            toast.error(data.message || "An error occurred");
        }
      } else if (error.request) {
        toast.error("Network error. Please check your connection.");
      } else {
        toast.error("An unexpected error occurred");
      }
      
      return Promise.reject(error);
    }
  );

  return instance;
};

// Use the utility function for date formatting

// API provider component
export const ApiProvider = ({ children }) => {
  const api = createApiInstance();

  // Region API methods
  const regionApi = {
    // Get all regions
    getAll: async () => {
      try {
        const response = await api.get("/regions");
        const responseData = response.data;

        // Handle the backend response format: { data: [...], meta: {...} }
        let regions = [];
        if (responseData && Array.isArray(responseData.data)) {
          regions = responseData.data;
        } else if (Array.isArray(responseData)) {
          regions = responseData;
        } else {
          console.warn("API response is not in expected format:", responseData);
          return [];
        }

        // Map backend fields to frontend expected format
        const mappedRegions = regions.map(region => ({
          id: region.id,
          name: region.name,
          addedBy: "System", // Backend doesn't provide this field yet
          addedOn: formatDateDisplay(region.created_at),
          branchCount: region.branchCount || 0,
          created_at: region.created_at,
          updated_at: region.updated_at
        }));

        return mappedRegions;
      } catch (error) {
        console.error("Error fetching regions:", error);
        // Return mock data if API is not available (for development)
        if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
          console.warn("API not available, returning mock data");
          return [
            {
              id: "1",
              name: "NYANZA",
              addedBy: "RENOIR",
              addedOn: "16 Jul 2025",
            },
            {
              id: "2",
              name: "CENTRAL",
              addedBy: "ADMIN",
              addedOn: "15 Jul 2025",
            },
            {
              id: "3",
              name: "EASTERN",
              addedBy: "USER",
              addedOn: "14 Jul 2025",
            }
          ];
        }
        throw error;
      }
    },

    // Get region by ID
    getById: async (id) => {
      try {
        const response = await api.get(`/regions/${id}`);
        return response.data;
      } catch (error) {
        throw error;
      }
    },

    // Create new region
    create: async (regionData) => {
      try {
        const response = await api.post("/regions", regionData);
        toast.success("Region created successfully!");

        // Map backend response to frontend format
        const createdRegion = response.data;
        return {
          id: createdRegion.id,
          name: createdRegion.name,
          addedBy: "System",
          addedOn: formatDateDisplay(createdRegion.created_at),
          branchCount: createdRegion.branchCount || 0,
          created_at: createdRegion.created_at,
          updated_at: createdRegion.updated_at
        };
      } catch (error) {
        // Return mock response if API is not available
        if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
          console.warn("API not available, simulating create");
          const mockResponse = {
            id: Date.now().toString(),
            ...regionData,
            addedBy: "CURRENT_USER",
            addedOn: formatDateDisplay(new Date().toISOString()),
          };
          toast.success("Region created successfully! (Mock)");
          return mockResponse;
        }
        throw error;
      }
    },

    // Update region
    update: async (id, regionData) => {
      try {
        const response = await api.patch(`/regions/${id}`, regionData);
        toast.success("Region updated successfully!");

        // Map backend response to frontend format
        const updatedRegion = response.data;
        return {
          id: updatedRegion.id,
          name: updatedRegion.name,
          addedBy: "System",
          addedOn: formatDateDisplay(updatedRegion.created_at),
          branchCount: updatedRegion.branchCount || 0,
          created_at: updatedRegion.created_at,
          updated_at: updatedRegion.updated_at
        };
      } catch (error) {
        // Return mock response if API is not available
        if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
          console.warn("API not available, simulating update");
          const mockResponse = {
            id,
            ...regionData,
            addedBy: "CURRENT_USER",
            addedOn: formatDateDisplay(new Date().toISOString()),
          };
          toast.success("Region updated successfully! (Mock)");
          return mockResponse;
        }
        throw error;
      }
    },

    // Delete region
    delete: async (id) => {
      try {
        const response = await api.delete(`/regions/${id}`);
        toast.success("Region deleted successfully!");
        return response.data;
      } catch (error) {
        // Return mock response if API is not available
        if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
          console.warn("API not available, simulating delete");
          toast.success("Region deleted successfully! (Mock)");
          return { success: true };
        }
        throw error;
      }
    },
  };

  // Branch API methods
  const branchApi = {
    // Get all branches
    getAll: async () => {
      try {
        const response = await api.get("/branches");
        const responseData = response.data;

        // Handle the backend response format: { data: [...], meta: {...} }
        let branches = [];
        if (responseData && Array.isArray(responseData.data)) {
          branches = responseData.data;
        } else if (Array.isArray(responseData)) {
          branches = responseData;
        } else {
          console.warn("API response is not in expected format:", responseData);
          return [];
        }

        // Map backend fields to frontend expected format
        const mappedBranches = branches.map(branch => ({
          id: branch.id,
          name: branch.name,
          region: branch.region?.name || branch.regionName || "N/A", // Handle different possible field names
          regionId: branch.region_id || branch.regionId || branch.region?.id, // Handle backend region_id field
          addedBy: "System", // Backend doesn't provide this field yet
          addedOn: formatDateDisplay(branch.created_at),
          created_at: branch.created_at,
          updated_at: branch.updated_at
        }));

        return mappedBranches;
      } catch (error) {
        console.error("Error fetching branches:", error);
        // Return mock data if API is not available (for development)
        if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
          console.warn("API not available, returning mock data");
          return [
            {
              id: "1",
              name: "NAIROBI BRANCH",
              region: "NAIROBI",
              addedBy: "RENOIR",
              addedOn: "16 Jul 2025",
            },
            {
              id: "2",
              name: "KISUMU BRANCH",
              region: "NYANZA",
              addedBy: "ADMIN",
              addedOn: "15 Jul 2025",
            },
            {
              id: "3",
              name: "THIKA BRANCH",
              region: "CENTRAL",
              addedBy: "USER",
              addedOn: "14 Jul 2025",
            }
          ];
        }
        throw error;
      }
    },

    // Get branch by ID
    getById: async (id) => {
      try {
        const response = await api.get(`/branches/${id}`);
        return response.data;
      } catch (error) {
        throw error;
      }
    },

    // Create new branch
    create: async (branchData) => {
      try {
        // Validate required fields
        if (!branchData.name || !branchData.name.trim()) {
          throw new Error("Branch name is required");
        }
        if (!branchData.regionId) {
          throw new Error("Region is required");
        }

        // Transform frontend data format to backend expected format
        const payload = {
          name: branchData.name.trim(),
          region_id: branchData.regionId, // Backend expects region_id, frontend sends regionId
        };

        console.log("Creating branch with payload:", payload);
        const response = await api.post("/branches", payload);
        toast.success("Branch created successfully!");

        // Map backend response to frontend format
        const createdBranch = response.data;
        return {
          id: createdBranch.id,
          name: createdBranch.name,
          region: createdBranch.region?.name || createdBranch.regionName || "N/A",
          regionId: createdBranch.region_id || createdBranch.regionId || createdBranch.region?.id, // Handle backend region_id field
          addedBy: "System",
          addedOn: formatDateDisplay(createdBranch.created_at),
          created_at: createdBranch.created_at,
          updated_at: createdBranch.updated_at
        };
      } catch (error) {
        // Return mock response if API is not available
        if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
          console.warn("API not available, simulating create");
          const mockResponse = {
            id: Date.now().toString(),
            ...branchData,
            addedBy: "CURRENT_USER",
            addedOn: formatDateDisplay(new Date().toISOString()),
          };
          toast.success("Branch created successfully! (Mock)");
          return mockResponse;
        }
        throw error;
      }
    },

    // Update branch
    update: async (id, branchData) => {
      try {
        // Validate required fields
        if (!branchData.name || !branchData.name.trim()) {
          throw new Error("Branch name is required");
        }
        if (!branchData.regionId) {
          throw new Error("Region is required");
        }

        // Transform frontend data format to backend expected format
        const payload = {
          name: branchData.name.trim(),
          region_id: branchData.regionId, // Backend expects region_id, frontend sends regionId
        };

        console.log("Updating branch with payload:", payload);
        const response = await api.patch(`/branches/${id}`, payload);
        toast.success("Branch updated successfully!");

        // Map backend response to frontend format
        const updatedBranch = response.data;
        return {
          id: updatedBranch.id,
          name: updatedBranch.name,
          region: updatedBranch.region?.name || updatedBranch.regionName || "N/A",
          regionId: updatedBranch.region_id || updatedBranch.regionId || updatedBranch.region?.id, // Handle backend region_id field
          addedBy: "System",
          addedOn: formatDateDisplay(updatedBranch.created_at),
          created_at: updatedBranch.created_at,
          updated_at: updatedBranch.updated_at
        };
      } catch (error) {
        // Return mock response if API is not available
        if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
          console.warn("API not available, simulating update");
          const mockResponse = {
            id,
            ...branchData,
            addedBy: "CURRENT_USER",
            addedOn: formatDateDisplay(new Date().toISOString()),
          };
          toast.success("Branch updated successfully! (Mock)");
          return mockResponse;
        }
        throw error;
      }
    },

    // Delete branch
    delete: async (id) => {
      try {
        const response = await api.delete(`/branches/${id}`);
        toast.success("Branch deleted successfully!");
        return response.data;
      } catch (error) {
        // Return mock response if API is not available
        if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
          console.warn("API not available, simulating delete");
          toast.success("Branch deleted successfully! (Mock)");
          return { success: true };
        }
        throw error;
      }
    },
  };

  // Permissions API methods
  const permissionsApi = {
    // Get all permissions
    getAll: async () => {
      try {
        const response = await api.get("/permissions");
        const responseData = response.data;

        if (responseData && Array.isArray(responseData.data)) {
          return responseData.data;
        } else if (Array.isArray(responseData)) {
          return responseData;
        } else {
          console.warn("API response is not in expected format:", responseData);
          return [];
        }
      } catch (error) {
        console.error("Error fetching permissions:", error);
        // Return mock data if API is not available
        if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
          console.warn("API not available, returning mock permissions");
          return [
            { id: "1", name: "View Items", description: "items", action: "view", resource: "items" },
            { id: "2", name: "Create Items", description: "items", action: "create", resource: "items" },
            { id: "3", name: "Edit Items", description: "items", action: "edit", resource: "items" },
            { id: "4", name: "Delete Items", description: "items", action: "delete", resource: "items" },
            { id: "5", name: "View Users", description: "administration", action: "view", resource: "users" },
            { id: "6", name: "Create Users", description: "administration", action: "create", resource: "users" },
            { id: "7", name: "View Leads", description: "leads", action: "view", resource: "leads" },
            { id: "8", name: "Create Leads", description: "leads", action: "create", resource: "leads" },
          ];
        }
        throw error;
      }
    },
  };

  // Roles API methods
  const rolesApi = {
    // Get all roles
    getAll: async () => {
      try {
        const response = await api.get("/roles");
        const responseData = response.data;

        if (responseData && Array.isArray(responseData.data)) {
          return responseData.data;
        } else if (Array.isArray(responseData)) {
          return responseData;
        } else {
          console.warn("API response is not in expected format:", responseData);
          return [];
        }
      } catch (error) {
        console.error("Error fetching roles:", error);
        // Return mock data if API is not available
        if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
          console.warn("API not available, returning mock roles");
          return [
            {
              id: "mock-1",
              name: "Administrator",
              description: "Full system access",
              userCount: 1,
              permissionCount: 8
            }
          ];
        }
        throw error;
      }
    },

    // Get role by ID
    getById: async (id) => {
      try {
        const response = await api.get(`/roles/${id}`);
        return response.data;
      } catch (error) {
        throw error;
      }
    },

    // Create new role
    create: async (roleData) => {
      try {
        const response = await api.post("/roles", roleData);
        toast.success("Role created successfully!");
        return response.data;
      } catch (error) {
        throw error;
      }
    },

    // Update role
    update: async (id, roleData) => {
      try {
        const response = await api.patch(`/roles/${id}`, roleData);
        toast.success("Role updated successfully!");
        return response.data;
      } catch (error) {
        throw error;
      }
    },

    // Delete role
    delete: async (id) => {
      try {
        const response = await api.delete(`/roles/${id}`);
        toast.success("Role deleted successfully!");
        return response.data;
      } catch (error) {
        throw error;
      }
    },
  };

  /**
   * User API methods
   *
   * Provides comprehensive user management functionality including:
   * - CRUD operations (Create, Read, Update, Delete)
   * - Pagination support
   * - Role and branch data fetching for form dropdowns
   * - Data formatting between backend and frontend formats
   * - Error handling with user-friendly toast notifications
   *
   * Backend Integration:
   * - Endpoint: /users
   * - Handles backend response format: { data: [...], meta: {...} }
   * - Maps backend fields (phone_number, role_id, branch_id) to frontend format
   * - Supports nested objects (role.name, branch.name, branch.region.name)
   */
  const userApi = {
    /**
     * Get all users with pagination and filtering support
     * @param {Object} params - Query parameters for filtering and pagination
     * @param {number} params.page - Page number (default: 1)
     * @param {number} params.limit - Items per page (default: 10)
     * @param {string} params.role - Filter by role name
     * @param {string} params.status - Filter by status
     * @returns {Promise<Object>} Response with data array and meta object
     */
    getAll: async (params = {}) => {
      try {
        const response = await api.get("/users", { params });
        const responseData = response.data;

        // Handle the backend response format: { data: [...], meta: {...} }
        let users = [];
        if (responseData && Array.isArray(responseData.data)) {
          users = responseData.data;
        } else if (Array.isArray(responseData)) {
          users = responseData;
        } else {
          console.warn("API response is not in expected format:", responseData);
          return { data: [], meta: {} };
        }

        // Map backend fields to frontend expected format
        const mappedUsers = users.map(user => ({
          id: user.id,
          name: user.name,
          email: user.email,
          phone: user.phone_number || "N/A", // Backend uses phone_number
          role: user.role?.name || "N/A", // Get role name from nested object
          roleId: user.role?.id,
          branch: user.branch?.name || "N/A", // Get branch name from nested object
          branchId: user.branch?.id,
          region: user.branch?.region?.name || "N/A", // Get region from nested branch object
          rmCode: user.rm_code,
          lastLogin: user.last_login ? formatDateDisplay(user.last_login) : "Never",
          addedBy: "System", // Backend doesn't provide this field yet
          addedOn: formatDateDisplay(user.created_at),
          // Status determination - users with recent login are active, others are inactive
          status: user.last_login ? "active" : "inactive",
          created_at: user.created_at,
          updated_at: user.updated_at,
          // Activity counts
          generalActivitiesCount: user.generalActivitiesCount || 0,
          leadsCount: user.leadsCount || 0,
          loanActivitiesCount: user.loanActivitiesCount || 0,
          scheduledVisitsCount: user.scheduledVisitsCount || 0,
          targetsCount: user.targetsCount || 0,
        }));

        return {
          data: mappedUsers,
          meta: responseData.meta || {}
        };
      } catch (error) {
        console.error("Error fetching users:", error);
        // Return mock data if API is not available (for development)
        if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
          console.warn("API not available, returning mock data");
          return {
            data: [
              {
                id: "user001",
                name: "John Doe",
                email: "<EMAIL>",
                phone: "+254712345678",
                role: "Administrator",
                branch: "Head Office",
                region: "Central",
                rmCode: "RM001",
                lastLogin: "Never",
                addedBy: "System Admin",
                addedOn: "15 Jan 2024",
                generalActivitiesCount: 5,
                leadsCount: 12,
                loanActivitiesCount: 3,
                scheduledVisitsCount: 8,
                targetsCount: 2,
              },
              {
                id: "user002",
                name: "Jane Smith",
                email: "<EMAIL>",
                phone: "+254723456789",
                role: "Manager",
                branch: "Nairobi Branch",
                region: "Central",
                rmCode: "RM002",
                lastLogin: "2 hours ago",
                addedBy: "John Doe",
                addedOn: "20 Jan 2024",
                generalActivitiesCount: 8,
                leadsCount: 15,
                loanActivitiesCount: 6,
                scheduledVisitsCount: 4,
                targetsCount: 3,
              }
            ],
            meta: { total: 2, page: 1, limit: 10, totalPages: 1 }
          };
        }
        throw error;
      }
    },

    // Get user by ID
    getById: async (id) => {
      try {
        const response = await api.get(`/users/${id}`);
        const user = response.data;

        // Map backend response to frontend format
        return {
          id: user.id,
          name: user.name,
          email: user.email,
          phone: user.phone_number,
          role: user.role?.id, // For form, we need the ID
          branch: user.branch?.id, // For form, we need the ID
          rmCode: user.rm_code,
          created_at: user.created_at,
          updated_at: user.updated_at,
        };
      } catch (error) {
        throw error;
      }
    },

    /**
     * Create new user
     * @param {Object} userData - User data from form
     * @param {string} userData.name - User's full name
     * @param {string} userData.email - User's email address
     * @param {string} userData.phone_number - User's phone number
     * @param {string} userData.role - Role ID (not role name)
     * @param {string} userData.branch - Branch ID (not branch name)
     * @param {string} userData.rmCode - RM code (optional)
     * @param {string} userData.password - User's password
     * @returns {Promise<Object>} Created user object in frontend format
     */
    create: async (userData) => {
      try {
        // Validate required fields
        if (!userData.name?.trim()) {
          throw new Error("Name is required");
        }
        if (!userData.email?.trim()) {
          throw new Error("Email is required");
        }
        if (!userData.role) {
          throw new Error("Role is required");
        }
        if (!userData.branch) {
          throw new Error("Branch is required");
        }
        if (!userData.password?.trim()) {
          throw new Error("Password is required");
        }

        // Format data for backend API expectations
        // Note: Frontend sends role/branch IDs, backend expects role_id/branch_id
        const payload = {
          name: userData.name.trim(),
          email: userData.email.trim(),
          phone_number: userData.phone_number?.trim() || userData.phone?.trim() || null, // Handle both field names
          role_id: userData.role, // Backend expects role_id
          branch_id: userData.branch, // Backend expects branch_id
          rm_code: userData.rmCode?.trim() || userData.rm_code?.trim() || null, // Handle both field names
          password: userData.password.trim(),
        };

        console.log("Creating user with form data:", userData);
        console.log("Creating user with payload:", payload);

        // Use longer timeout for user creation (password hashing can be slow)
        const response = await api.post("/users", payload, {
          timeout: 30000, // 30 seconds timeout for user creation
        });
        console.log("User created successfully:", response.data);
        toast.success("User created successfully!");

        // Map backend response to frontend format
        const createdUser = response.data;
        return {
          id: createdUser.id,
          name: createdUser.name,
          email: createdUser.email,
          phone: createdUser.phone_number || "N/A",
          role: createdUser.role?.name || "N/A",
          branch: createdUser.branch?.name || "N/A",
          region: createdUser.branch?.region?.name || "N/A",
          rmCode: createdUser.rm_code,
          lastLogin: "Never",
          addedBy: "System",
          addedOn: formatDateDisplay(createdUser.created_at),
          created_at: createdUser.created_at,
          updated_at: createdUser.updated_at,
        };
      } catch (error) {
        console.error("Error creating user:", error);

        // Handle timeout errors specifically
        if (error.code === 'ECONNABORTED') {
          console.error("Request timed out");
          toast.error("Request timed out. The server may be busy. Please try again.");
        }
        // Log detailed error information
        else if (error.response) {
          console.error("Backend error response:", error.response.data);
          console.error("Status code:", error.response.status);

          // Show specific backend error message if available
          if (error.response.data?.message) {
            toast.error(`Failed to create user: ${error.response.data.message}`);
          } else if (error.response.data?.error) {
            toast.error(`Failed to create user: ${error.response.data.error}`);
          } else {
            toast.error(`Failed to create user: ${error.response.status} ${error.response.statusText}`);
          }
        } else if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
          console.warn("API not available, simulating create");
          const mockResponse = {
            id: Date.now().toString(),
            name: userData.name,
            email: userData.email,
            phone: userData.phone_number || userData.phone,
            role: "User", // Mock role
            branch: "Main Branch", // Mock branch
            region: "Central",
            rmCode: userData.rmCode,
            lastLogin: "Never",
            addedBy: "CURRENT_USER",
            addedOn: formatDateDisplay(new Date().toISOString()),
          };
          toast.success("User created successfully! (Mock)");
          return mockResponse;
        } else {
          toast.error("Failed to create user. Please try again.");
        }
        throw error;
      }
    },

    // Update user
    update: async (id, userData) => {
      try {
        // Format data for backend (role and branch should be IDs)
        const payload = {
          name: userData.name?.trim(),
          email: userData.email?.trim(),
          phone_number: userData.phone_number?.trim() || userData.phone?.trim(), // Handle both field names
          role_id: userData.role,
          branch_id: userData.branch,
          rm_code: userData.rmCode?.trim() || userData.rm_code?.trim() || null, // Handle both field names
        };

        // Only include password if provided
        if (userData.password && userData.password.trim()) {
          payload.password = userData.password.trim();
        }

        const response = await api.patch(`/users/${id}`, payload, {
          timeout: 30000, // 30 seconds timeout for user update
        });
        toast.success("User updated successfully!");

        // Map backend response to frontend format
        const updatedUser = response.data;
        return {
          id: updatedUser.id,
          name: updatedUser.name,
          email: updatedUser.email,
          phone: updatedUser.phone_number || "N/A",
          role: updatedUser.role?.name || "N/A",
          branch: updatedUser.branch?.name || "N/A",
          region: updatedUser.branch?.region?.name || "N/A",
          rmCode: updatedUser.rm_code,
          lastLogin: updatedUser.last_login ? formatDateDisplay(updatedUser.last_login) : "Never",
          addedBy: "System",
          addedOn: formatDateDisplay(updatedUser.created_at),
          created_at: updatedUser.created_at,
          updated_at: updatedUser.updated_at,
        };
      } catch (error) {
        // Return mock response if API is not available
        if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
          console.warn("API not available, simulating update");
          const mockResponse = {
            id,
            name: userData.name,
            email: userData.email,
            phone: userData.phone,
            role: "User",
            branch: "Main Branch",
            region: "Central",
            rmCode: userData.rmCode,
            lastLogin: "Never",
            addedBy: "CURRENT_USER",
            addedOn: formatDateDisplay(new Date().toISOString()),
          };
          toast.success("User updated successfully! (Mock)");
          return mockResponse;
        }
        throw error;
      }
    },

    // Delete user
    delete: async (id) => {
      try {
        const response = await api.delete(`/users/${id}`);
        toast.success("User deleted successfully!");
        return response.data;
      } catch (error) {
        // Return mock response if API is not available
        if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
          console.warn("API not available, simulating delete");
          toast.success("User deleted successfully! (Mock)");
          return { success: true };
        }
        throw error;
      }
    },

    // Get roles for dropdown (reuse from rolesApi but format for user forms)
    getRoles: async () => {
      try {
        const response = await api.get("/roles");
        const responseData = response.data;

        let roles = [];
        if (responseData && Array.isArray(responseData.data)) {
          roles = responseData.data;
        } else if (Array.isArray(responseData)) {
          roles = responseData;
        }

        // Format for react-select dropdown
        return roles.map(role => ({
          value: role.id,
          label: role.name,
          description: role.description,
        }));
      } catch (error) {
        console.error("Error fetching roles:", error);
        // Return mock data for development
        return [
          { value: "1", label: "Administrator", description: "Full system access" },
          { value: "2", label: "Manager", description: "Management access" },
          { value: "3", label: "User", description: "Basic user access" },
        ];
      }
    },

    // Get branches for dropdown (reuse from branchApi but format for user forms)
    getBranches: async () => {
      try {
        const response = await api.get("/branches");
        const responseData = response.data;

        let branches = [];
        if (responseData && Array.isArray(responseData.data)) {
          branches = responseData.data;
        } else if (Array.isArray(responseData)) {
          branches = responseData;
        }

        // Format for react-select dropdown
        return branches.map(branch => ({
          value: branch.id,
          label: branch.name,
          region: branch.region?.name || "N/A",
        }));
      } catch (error) {
        console.error("Error fetching branches:", error);
        // Return mock data for development
        return [
          { value: "1", label: "Head Office", region: "Central" },
          { value: "2", label: "Nairobi Branch", region: "Central" },
          { value: "3", label: "Mombasa Branch", region: "Coast" },
        ];
      }
    },
  };

  const value = {
    api,
    regionApi,
    branchApi,
    permissionsApi,
    rolesApi,
    userApi,
  };

  return <ApiContext.Provider value={value}>{children}</ApiContext.Provider>;
};

export default ApiContext;
