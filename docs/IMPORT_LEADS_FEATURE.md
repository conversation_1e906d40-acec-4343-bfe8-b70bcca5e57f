# Import Leads Feature Documentation

## Overview

The Import Leads feature allows users to bulk import leads from Excel (.xlsx, .xls) or CSV files while assigning a selected anchor to all imported leads. This feature includes template generation, data validation, and anchor assignment functionality.

## Features

### 1. Excel Template Generation
- **Downloadable Template**: Users can download a pre-formatted Excel template with descriptive column headers
- **Sample Data**: Template includes 5 realistic sample records showing different customer types
- **Built-in Instructions**: Instructions and notes are embedded at the top of the template
- **Clear Column Headers**: Headers include format hints and requirements (e.g., "Phone Number * (07xxxxxxxx)")

### 2. Anchor Assignment
- **Searchable Dropdown**: Users can search and select an anchor from existing leads
- **Bulk Assignment**: Selected anchor is assigned to all leads in the import file
- **Real-time Search**: Anchor selection supports real-time search functionality

### 3. File Processing (Backend)
- **Server-Side Processing**: File parsing and validation handled by backend
- **Multiple Formats**: Supports Excel (.xlsx, .xls) and CSV files
- **FormData Upload**: Raw file sent to backend with anchor ID
- **API Response**: JSON response with success/error status and import count

## Implementation Details

### Files Modified/Created

#### New Files:
1. **`src/utils/excelUtils.js`** - Excel/CSV processing utilities
2. **`docs/IMPORT_LEADS_FEATURE.md`** - This documentation file

#### Modified Files:
1. **`src/components/modals/ImportModal.jsx`** - Enhanced with anchor selection
2. **`src/components/common/DataTable.jsx`** - Added enhanced import props
3. **`src/pages/Hitlist.jsx`** - Integrated import functionality
4. **`src/services/leadsService.js`** - Added bulk import and anchor fetching methods
5. **`package.json`** - Added xlsx and papaparse dependencies

### Dependencies Added
```json
{
  "xlsx": "^0.18.5"
}
```
Note: `papaparse` was removed as file parsing is now handled by the backend.

### Template Fields

The Excel template includes the following fields:

| Column Header | Required | Description |
|---------------|----------|-------------|
| Customer Full Name * | ✅ | Full name of the customer |
| Phone Number * (07xxxxxxxx) | ✅ | Valid Kenyan phone number format |
| Customer Category (Employed/Self Employed/etc) | ❌ | Type of customer with examples |
| Industry Sector (ISIC) | ❌ | Industry classification |
| Lead Type (New/Existing) | ❌ | "New" or "Existing" (defaults to "New") |
| Client ID (Required for Existing) | ❌ | Required only for "Existing" leads |
| Branch Name | ❌ | Branch to assign the lead to |
| Contact Person Name | ❌ | Alternative contact person |
| Contact Person Phone | ❌ | Alternative contact phone |
| Employer/Company Name | ❌ | Customer's employer or company |

### API Endpoints

#### New Endpoints Required:
1. **`POST /api/leads/upload-excel`** - Import leads from file with anchor assignment
   - **Content-Type**: `multipart/form-data`
   - **Body**:
     - `file`: The uploaded Excel/CSV file
     - `anchorId`: The selected anchor ID
   - **Response**:
     ```json
     {
       "success": true,
       "inserted": 25,
       "message": "Successfully imported 25 leads"
     }
     ```

2. **`GET /leads/anchors`** - Get leads that can serve as anchors
   ```json
   {
     "data": [
       {
         "id": "lead123",
         "lead_name": "Anchor Lead",
         "phoneNumber": "0712345678"
       }
     ]
   }
   ```

## Usage Instructions

### For Users:

1. **Navigate to Hitlist Page**
   - Go to the Leads/Hitlist section
   - Click the "Import" button

2. **Download Template**
   - Click "Download Template" to get the Excel template
   - The template includes sample data and instructions

3. **Prepare Data**
   - Fill in your lead data using the template format
   - Remove sample data before importing
   - Ensure required fields are completed

4. **Select Anchor**
   - Choose an anchor from the searchable dropdown
   - This anchor will be assigned to all imported leads

5. **Upload File**
   - Drag and drop or select your Excel/CSV file
   - Click "Import Data" to process

6. **Review Results**
   - Success message shows number of leads imported
   - Any errors will be displayed with specific details

### For Developers:

#### Adding Import to Other Pages:

```jsx
// In your page component
import { downloadTemplate, parseFile } from "../utils/excelUtils";

// Add to DataTable props
<DataTable
  showImportExport={true}
  showAnchorSelection={true} // For leads import
  anchors={anchors}
  onDownloadTemplate={handleDownloadTemplate}
  isLeadsImport={true}
  onImportModalOpen={handleImportModalOpen}
  onImport={handleImport}
  // ... other props
/>
```

#### Customizing Template:

```javascript
// In excelUtils.js
export const CUSTOM_TEMPLATE_FIELDS = [
  { key: 'fieldName', label: 'Field Label *', required: true },
  // ... add your fields
];
```

## Error Handling

### Validation Errors:
- Missing required fields
- Invalid phone number format
- Missing Client ID for existing leads
- File format not supported
- File too large (>1000 leads)

### Network Errors:
- API connection failures
- Server-side validation errors
- Timeout errors

## Testing

### Manual Testing:
1. Download template and verify format
2. Test with valid data file
3. Test with invalid data (missing required fields)
4. Test anchor selection functionality
5. Verify error messages display correctly

### Automated Testing:
```javascript
// Example test
import { parseLeadsFile } from '../utils/excelUtils';

test('should parse valid CSV file', async () => {
  const csvData = 'Customer Name *,Phone Number *\nJohn Doe,0712345678';
  const file = new File([csvData], 'test.csv', { type: 'text/csv' });
  const leads = await parseLeadsFile(file);
  expect(leads).toHaveLength(1);
  expect(leads[0].customerName).toBe('John Doe');
});
```

## Future Enhancements

1. **Progress Indicator**: Show import progress for large files
2. **Duplicate Detection**: Check for duplicate leads before import
3. **Field Mapping**: Allow users to map custom fields
4. **Import History**: Track and display import history
5. **Bulk Edit**: Allow editing of imported leads before saving
6. **Advanced Validation**: Custom validation rules per field
7. **Template Customization**: Allow users to customize template fields

## Troubleshooting

### Common Issues:

1. **Template Download Fails**
   - Check browser popup blockers
   - Ensure sufficient disk space

2. **Import Fails with "No anchors found"**
   - Ensure there are existing leads in the system
   - Check API connectivity

3. **File Upload Fails**
   - Verify file format (.xlsx, .xls, .csv)
   - Check file size (max 1000 leads)
   - Ensure required fields are present

4. **Anchor Selection Not Working**
   - Check if anchors are being fetched
   - Verify API endpoint is working
   - Check console for JavaScript errors

## Support

For technical support or feature requests, please contact the development team or create an issue in the project repository.
